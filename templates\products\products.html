{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-boxes me-2"></i>
            إدارة المنتجات
        </h1>
        <p class="text-muted">عرض وإدارة جميع المنتجات في النظام</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <a href="{{ url_for('products.add_product') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('products.products') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    {{ form.search.label(class="form-label") }}
                    {{ form.search() }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.category_id.label(class="form-label") }}
                    {{ form.category_id() }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.warehouse_id.label(class="form-label") }}
                    {{ form.warehouse_id() }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.product_type.label(class="form-label") }}
                    {{ form.product_type() }}
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="form-check">
                        {{ form.low_stock() }}
                        {{ form.low_stock.label(class="form-check-label") }}
                    </div>
                </div>
                <div class="col-md-1 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        {{ form.submit(class="btn btn-primary w-100") }}
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المنتجات</h5>
        <div>
            <span class="badge bg-primary">{{ products.total }} منتج</span>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('products.barcode_labels') }}" class="btn btn-sm btn-outline-primary ms-2">
                <i class="fas fa-barcode me-1"></i>
                طباعة ملصقات
            </a>
            {% endif %}
        </div>
    </div>
    <div class="card-body p-0">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الباركود</th>
                        <th>اسم المنتج</th>
                        <th>التصنيف</th>
                        <th>المستودع</th>
                        <th>الكمية</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr class="{% if product.quantity <= product.min_quantity %}table-warning{% endif %}">
                        <td>
                            <code>{{ product.barcode }}</code>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if product.image_path %}
                                <img src="{{ url_for('static', filename='uploads/products/' + product.image_path) }}" 
                                     alt="{{ product.name }}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                {% else %}
                                <div class="me-2 bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px; border-radius: 4px;">
                                    <i class="fas fa-box text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ product.name }}</strong>
                                    {% if product.description %}
                                    <br><small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>{{ product.category_ref.name }}</td>
                        <td>{{ product.warehouse_ref.name }}</td>
                        <td>
                            <span class="{% if product.quantity <= product.min_quantity %}text-warning fw-bold{% endif %}">
                                {{ product.quantity }} {{ product.unit_ref.name }}
                            </span>
                            {% if product.quantity <= product.min_quantity %}
                            <br><small class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                أقل من الحد الأدنى ({{ product.min_quantity }})
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{% if product.product_type == 'raw' %}info{% else %}success{% endif %}">
                                {% if product.product_type == 'raw' %}مادة خام{% else %}منتج نهائي{% endif %}
                            </span>
                        </td>
                        <td>{{ product.condition_ref.name }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('products.product_detail', id=product.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_edit() %}
                                <a href="{{ url_for('products.edit_product', id=product.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.can_delete() %}
                                <form method="POST" action="{{ url_for('products.delete_product', id=product.id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف المنتج {{ product.name }}؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Products pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('products.products', page=products.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('products.products', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('products.products', page=products.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('products.add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول منتج
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if current_user.can_edit() %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('products.add_product') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus-circle d-block mb-2" style="font-size: 2rem;"></i>
                            إضافة منتج
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('products.barcode_labels') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-barcode d-block mb-2" style="font-size: 2rem;"></i>
                            طباعة ملصقات
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('products.products', low_stock=1) }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-exclamation-triangle d-block mb-2" style="font-size: 2rem;"></i>
                            المنتجات الناقصة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('warehouses.categories') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-tags d-block mb-2" style="font-size: 2rem;"></i>
                            إدارة التصنيفات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-submit search form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('select[name="category_id"], select[name="warehouse_id"], select[name="product_type"]');
    const lowStockCheckbox = document.querySelector('input[name="low_stock"]');
    
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    if (lowStockCheckbox) {
        lowStockCheckbox.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
{% endblock %}
