{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-ruler me-2"></i>
            إدارة الوحدات
        </h1>
        <p class="text-muted">عرض وإدارة وحدات القياس للمنتجات</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUnitModal">
            <i class="fas fa-plus me-2"></i>
            إضافة وحدة جديدة
        </button>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('warehouses.units') }}">
            <div class="row">
                <div class="col-md-10 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="البحث في الوحدات..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Units Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الوحدات</h5>
        <span class="badge bg-primary">{{ units.total }} وحدة</span>
    </div>
    <div class="card-body p-0">
        {% if units.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>اسم الوحدة</th>
                        <th>الرمز</th>
                        <th>النوع</th>
                        <th>عدد المنتجات</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for unit in units.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ unit.name }}</strong>
                                {% if unit.description %}
                                <br><small class="text-muted">{{ unit.description[:50] }}{% if unit.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ unit.symbol }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {% if unit.unit_type == 'weight' %}وزن
                                {% elif unit.unit_type == 'volume' %}حجم
                                {% elif unit.unit_type == 'length' %}طول
                                {% elif unit.unit_type == 'area' %}مساحة
                                {% elif unit.unit_type == 'count' %}عدد
                                {% else %}أخرى{% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ unit.products|length }}</span>
                        </td>
                        <td>
                            <span class="badge bg-{% if unit.is_active %}success{% else %}danger{% endif %}">
                                {% if unit.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('products.products', unit_id=unit.id) }}" 
                                   class="btn btn-outline-primary" title="عرض المنتجات">
                                    <i class="fas fa-boxes"></i>
                                </a>
                                {% if current_user.can_edit() %}
                                <button type="button" class="btn btn-outline-warning" title="تعديل"
                                        onclick="editUnit({{ unit.id }}, '{{ unit.name }}', '{{ unit.symbol }}', '{{ unit.unit_type }}', '{{ unit.description or '' }}', {{ unit.is_active|lower }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% endif %}
                                {% if current_user.can_delete() and unit.products|length == 0 %}
                                <form method="POST" action="{{ url_for('warehouses.delete_unit', id=unit.id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف الوحدة {{ unit.name }}؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if units.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Units pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if units.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.units', page=units.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in units.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != units.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('warehouses.units', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if units.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.units', page=units.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد وحدات</h5>
            <p class="text-muted">لم يتم العثور على وحدات تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUnitModal">
                <i class="fas fa-plus me-2"></i>
                إضافة أول وحدة
            </button>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Unit Modal -->
<div class="modal fade" id="addUnitModal" tabindex="-1" aria-labelledby="addUnitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUnitModalLabel">إضافة وحدة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('warehouses.add_unit') }}">
                {{ add_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ add_form.name.label(class="form-label required") }}
                        {{ add_form.name(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.symbol.label(class="form-label required") }}
                        {{ add_form.symbol(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.unit_type.label(class="form-label required") }}
                        {{ add_form.unit_type(class="form-select") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.description.label(class="form-label") }}
                        {{ add_form.description(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ add_form.is_active(class="form-check-input", checked=true) }}
                            {{ add_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ add_form.submit(class="btn btn-success") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Unit Modal -->
<div class="modal fade" id="editUnitModal" tabindex="-1" aria-labelledby="editUnitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUnitModalLabel">تعديل الوحدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="editUnitForm">
                {{ edit_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ edit_form.name.label(class="form-label required") }}
                        {{ edit_form.name(class="form-control", id="edit_name") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.symbol.label(class="form-label required") }}
                        {{ edit_form.symbol(class="form-control", id="edit_symbol") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.unit_type.label(class="form-label required") }}
                        {{ edit_form.unit_type(class="form-select", id="edit_unit_type") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.description.label(class="form-label") }}
                        {{ edit_form.description(class="form-control", id="edit_description") }}
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ edit_form.is_active(class="form-check-input", id="edit_is_active") }}
                            {{ edit_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ edit_form.submit(class="btn btn-warning") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editUnit(id, name, symbol, unitType, description, isActive) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_symbol').value = symbol;
    document.getElementById('edit_unit_type').value = unitType;
    document.getElementById('edit_description').value = description;
    document.getElementById('edit_is_active').checked = isActive;
    
    const form = document.getElementById('editUnitForm');
    form.action = `/warehouses/units/${id}/edit`;
    
    const modal = new bootstrap.Modal(document.getElementById('editUnitModal'));
    modal.show();
}

// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}
