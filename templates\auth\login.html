<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المستودعات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-warehouse"></i>
                <h2 class="login-title">نظام إدارة المستودعات</h2>
            </div>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="mb-3">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username() }}
                    {% if form.username.errors %}
                        <div class="text-danger small">
                            {% for error in form.username.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password() }}
                    {% if form.password.errors %}
                        <div class="text-danger small">
                            {% for error in form.password.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-3 form-check">
                    {{ form.remember_me() }}
                    {{ form.remember_me.label(class="form-check-label") }}
                </div>
                
                <div class="d-grid">
                    {{ form.submit() }}
                </div>
            </form>
            
            <div class="text-center mt-4">
                <small class="text-muted">
                    المستخدم الافتراضي: admin<br>
                    كلمة المرور: admin123
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
