<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة ملصقات الباركود - {{ product.name }}</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 8px;
            line-height: 1.2;
            background: white;
        }
        
        .page {
            width: 210mm;
            height: 297mm;
            padding: 5mm;
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .label {
            width: 38.1mm;
            height: 21.2mm;
            border: 1px solid #ddd;
            margin: 1mm;
            padding: 1mm;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background: white;
            box-sizing: border-box;
        }
        
        .barcode-container {
            width: 100%;
            height: 12mm;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1mm;
        }
        
        .barcode {
            height: 10mm;
            max-width: 100%;
        }
        
        .barcode-fallback {
            background: repeating-linear-gradient(
                90deg,
                #000 0px,
                #000 1px,
                #fff 1px,
                #fff 2px
            );
            height: 10mm;
            width: 30mm;
            border: 1px solid #000;
        }
        
        .product-name {
            font-size: 7px;
            font-weight: bold;
            margin-bottom: 0.5mm;
            line-height: 1.1;
            max-height: 4mm;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }
        
        .barcode-number {
            font-family: 'Courier New', monospace;
            font-size: 6px;
            font-weight: bold;
            letter-spacing: 0.5px;
        }
        
        .no-print {
            display: block;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .label {
                border: 1px solid #000 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        
        /* Print controls */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .print-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-controls button:hover {
            background: #0056b3;
        }
        
        .print-controls .btn-secondary {
            background: #6c757d;
        }
        
        .print-controls .btn-secondary:hover {
            background: #545b62;
        }
        
        .info-panel {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <div class="info-panel">
            <strong>{{ product.name }}</strong><br>
            الباركود: <code>{{ product.barcode }}</code><br>
            عدد الملصقات: {{ copies }}<br>
            عدد الصفحات: {{ (copies / 65) | round(0, 'ceil') | int }}
        </div>
        <button onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button onclick="window.close()" class="btn-secondary">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    {% set labels_per_page = 65 %}
    {% set current_label = 0 %}
    
    {% for page_num in range((copies / labels_per_page) | round(0, 'ceil') | int) %}
    <div class="page">
        {% for label_num in range(labels_per_page) %}
            {% set current_label = current_label + 1 %}
            {% if current_label <= copies %}
            <div class="label">
                <div class="barcode-container">
                    {% if barcode_image %}
                    <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode" class="barcode">
                    {% else %}
                    <div class="barcode-fallback"></div>
                    {% endif %}
                </div>
                <div class="product-name">{{ product.name }}</div>
                <div class="barcode-number">{{ product.barcode }}</div>
            </div>
            {% endif %}
        {% endfor %}
    </div>
    {% endfor %}

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully rendered
            setTimeout(function() {
                window.print();
            }, 500);
        };
        
        // Close window after printing (optional)
        window.onafterprint = function() {
            // Uncomment the line below if you want to auto-close after printing
            // window.close();
        };
    </script>
</body>
</html>
