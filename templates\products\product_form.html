{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-{% if action == 'add' %}plus{% else %}edit{% endif %} me-2"></i>
            {% if action == 'add' %}إضافة منتج جديد{% else %}تعديل المنتج{% endif %}
        </h1>
        {% if action == 'edit' %}
        <p class="text-muted">تعديل بيانات المنتج: {{ product.name }}</p>
        {% endif %}
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('products.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    بيانات المنتج
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="product-form" data-auto-save="true">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Barcode -->
                        <div class="col-md-6 mb-3">
                            {{ form.barcode.label(class="form-label required") }}
                            <div class="input-group">
                                {{ form.barcode(class="form-control" + (" is-invalid" if form.barcode.errors else "")) }}
                                <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()" title="توليد باركود">
                                    <i class="fas fa-random"></i>
                                </button>
                            </div>
                            {% if form.barcode.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.barcode.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Product Name -->
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label required") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- Category -->
                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label required") }}
                            {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                            {% if form.category_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.category_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Unit -->
                        <div class="col-md-6 mb-3">
                            {{ form.unit_id.label(class="form-label required") }}
                            {{ form.unit_id(class="form-select" + (" is-invalid" if form.unit_id.errors else "")) }}
                            {% if form.unit_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.unit_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Condition -->
                        <div class="col-md-6 mb-3">
                            {{ form.condition_id.label(class="form-label required") }}
                            {{ form.condition_id(class="form-select" + (" is-invalid" if form.condition_id.errors else "")) }}
                            {% if form.condition_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.condition_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Warehouse -->
                        <div class="col-md-6 mb-3">
                            {{ form.warehouse_id.label(class="form-label required") }}
                            {{ form.warehouse_id(class="form-select" + (" is-invalid" if form.warehouse_id.errors else "")) }}
                            {% if form.warehouse_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.warehouse_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Quantity -->
                        <div class="col-md-4 mb-3">
                            {{ form.quantity.label(class="form-label required") }}
                            {{ form.quantity(class="form-control" + (" is-invalid" if form.quantity.errors else "")) }}
                            {% if form.quantity.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.quantity.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Min Quantity -->
                        <div class="col-md-4 mb-3">
                            {{ form.min_quantity.label(class="form-label") }}
                            {{ form.min_quantity(class="form-control" + (" is-invalid" if form.min_quantity.errors else "")) }}
                            {% if form.min_quantity.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.min_quantity.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Product Type -->
                        <div class="col-md-4 mb-3">
                            {{ form.product_type.label(class="form-label required") }}
                            {{ form.product_type(class="form-select" + (" is-invalid" if form.product_type.errors else "")) }}
                            {% if form.product_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.product_type.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Image -->
                    <div class="mb-3">
                        {{ form.image.label(class="form-label") }}
                        {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else "")) }}
                        {% if form.image.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.image.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                    </div>
                    
                    <!-- Notes -->
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Active Status -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input" + (" is-invalid" if form.is_active.errors else "")) }}
                            {{ form.is_active.label(class="form-check-label") }}
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-success btn-lg") }}
                            <a href="{{ url_for('products.products') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        {% if action == 'edit' %}
                        <div>
                            <a href="{{ url_for('products.product_detail', id=product.id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Image Preview -->
        {% if action == 'edit' and product.image_path %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>
                    الصورة الحالية
                </h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='uploads/products/' + product.image_path) }}" 
                     alt="{{ product.name }}" class="img-fluid rounded" style="max-height: 200px;">
            </div>
        </div>
        {% endif %}
        
        <!-- Barcode Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-barcode me-2"></i>
                    معاينة الباركود
                </h5>
            </div>
            <div class="card-body text-center">
                <div id="barcode-preview">
                    {% if action == 'edit' %}
                    <img src="data:image/png;base64,{{ create_barcode_image(product.barcode) }}" 
                         alt="Barcode" class="img-fluid">
                    <p class="mt-2 mb-0"><code>{{ product.barcode }}</code></p>
                    {% else %}
                    <p class="text-muted">أدخل الباركود لمعاينته</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Help -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>الباركود:</strong> يجب أن يكون فريداً لكل منتج
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <strong>الحد الأدنى:</strong> سيظهر تنبيه عند انخفاض الكمية
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-tag text-success me-2"></i>
                        <strong>النوع:</strong> مادة خام أو منتج نهائي
                    </li>
                    <li>
                        <i class="fas fa-save text-primary me-2"></i>
                        <strong>الحفظ التلقائي:</strong> يتم حفظ المسودة كل 30 ثانية
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Generate random barcode
function generateBarcode() {
    fetch('{{ url_for("products.generate_barcode") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('barcode').value = data.barcode;
            updateBarcodePreview(data.barcode);
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في توليد الباركود', 'danger');
        });
}

// Update barcode preview
function updateBarcodePreview(barcode) {
    if (barcode && barcode.length >= 10) {
        const preview = document.getElementById('barcode-preview');
        preview.innerHTML = `
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 mb-0">جاري إنشاء الباركود...</p>
        `;
        
        // Simulate barcode generation (in real implementation, you'd call an API)
        setTimeout(() => {
            preview.innerHTML = `
                <div class="bg-light p-3 rounded">
                    <div style="background: repeating-linear-gradient(90deg, #000 0px, #000 2px, #fff 2px, #fff 4px); height: 60px;"></div>
                </div>
                <p class="mt-2 mb-0"><code>${barcode}</code></p>
            `;
        }, 1000);
    }
}

// Listen for barcode input changes
document.addEventListener('DOMContentLoaded', function() {
    const barcodeInput = document.getElementById('barcode');
    if (barcodeInput) {
        barcodeInput.addEventListener('input', function() {
            updateBarcodePreview(this.value);
        });
    }
    
    // Form validation
    const form = document.getElementById('product-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm('product-form')) {
                e.preventDefault();
            }
        });
    }
});

// Image preview
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image');
    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create or update image preview
                    let previewCard = document.querySelector('.image-preview-card');
                    if (!previewCard) {
                        previewCard = document.createElement('div');
                        previewCard.className = 'card mb-4 image-preview-card';
                        previewCard.innerHTML = `
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-image me-2"></i>
                                    معاينة الصورة الجديدة
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <img class="img-fluid rounded preview-image" style="max-height: 200px;">
                            </div>
                        `;
                        document.querySelector('.col-lg-4').insertBefore(previewCard, document.querySelector('.col-lg-4').firstChild);
                    }
                    
                    const previewImage = previewCard.querySelector('.preview-image');
                    previewImage.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
</script>
{% endblock %}
