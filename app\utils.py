import os
import secrets
import random
import string
from functools import wraps
from flask import abort, current_app, flash, redirect, url_for
from flask_login import current_user
from PIL import Image
import barcode
from barcode.writer import ImageWriter
from io import BytesIO
import base64

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def can_edit_required(f):
    """Decorator to require edit permissions (admin or employee)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.can_edit():
            flash('ليس لديك صلاحية لتعديل البيانات', 'danger')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def can_delete_required(f):
    """Decorator to require delete permissions (admin only)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.can_delete():
            flash('ليس لديك صلاحية لحذف البيانات', 'danger')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def save_picture(form_picture, folder):
    """Save uploaded picture and return filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(current_app.config['UPLOAD_FOLDER'], folder, picture_fn)
    
    # Create folder if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)
    
    # Resize image
    output_size = (800, 600)
    img = Image.open(form_picture)
    img.thumbnail(output_size)
    img.save(picture_path)
    
    return os.path.join(folder, picture_fn)

def generate_barcode_number():
    """Generate a random barcode number"""
    return ''.join(random.choices(string.digits, k=13))

def create_barcode_image(barcode_data):
    """Create barcode image and return base64 encoded string"""
    try:
        # Use EAN13 barcode format
        ean = barcode.get('ean13', barcode_data, writer=ImageWriter())
        buffer = BytesIO()
        ean.write(buffer)
        buffer.seek(0)
        
        # Convert to base64
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/png;base64,{img_base64}"
    except Exception as e:
        return None

def format_currency(amount):
    """Format currency with Arabic locale"""
    if amount is None:
        return "0.00"
    return f"{amount:,.2f}"

def get_next_invoice_number(prefix='INV'):
    """Generate next invoice number"""
    from datetime import datetime
    from app.models import InvoiceIn, InvoiceOut, ProductionInvoice
    
    today = datetime.now()
    date_str = today.strftime('%Y%m%d')
    
    # Get the highest number for today
    if prefix == 'IN':
        last_invoice = InvoiceIn.query.filter(
            InvoiceIn.invoice_number.like(f'{prefix}-{date_str}-%')
        ).order_by(InvoiceIn.invoice_number.desc()).first()
    elif prefix == 'OUT':
        last_invoice = InvoiceOut.query.filter(
            InvoiceOut.invoice_number.like(f'{prefix}-{date_str}-%')
        ).order_by(InvoiceOut.invoice_number.desc()).first()
    elif prefix == 'PROD':
        last_invoice = ProductionInvoice.query.filter(
            ProductionInvoice.invoice_number.like(f'{prefix}-{date_str}-%')
        ).order_by(ProductionInvoice.invoice_number.desc()).first()
    else:
        last_invoice = None
    
    if last_invoice:
        # Extract the sequence number
        parts = last_invoice.invoice_number.split('-')
        if len(parts) >= 3:
            try:
                last_seq = int(parts[2])
                next_seq = last_seq + 1
            except ValueError:
                next_seq = 1
        else:
            next_seq = 1
    else:
        next_seq = 1
    
    return f"{prefix}-{date_str}-{next_seq:04d}"

def allowed_file(filename, allowed_extensions={'png', 'jpg', 'jpeg', 'gif'}):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def create_backup_filename():
    """Create backup filename with timestamp"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"warehouse_backup_{timestamp}.db"

def get_company_settings():
    """Get company settings or create default"""
    from app.models import CompanySettings
    settings = CompanySettings.query.first()
    if not settings:
        settings = CompanySettings(
            company_name='نظام إدارة المستودعات',
            address='',
            phone='',
            email='',
            tax_number=''
        )
        from app import db
        db.session.add(settings)
        db.session.commit()
    return settings

def log_stock_movement(product_id, movement_type, quantity, reference_type=None, reference_id=None, notes=None):
    """Log stock movement"""
    from app.models import StockMovement
    from app import db
    
    movement = StockMovement(
        product_id=product_id,
        movement_type=movement_type,
        quantity=quantity,
        reference_type=reference_type,
        reference_id=reference_id,
        notes=notes,
        created_by=current_user.id if current_user.is_authenticated else None
    )
    db.session.add(movement)
    db.session.commit()

def update_product_quantity(product_id, quantity_change, movement_type, reference_type=None, reference_id=None, notes=None):
    """Update product quantity and log movement"""
    from app.models import Product
    from app import db
    
    product = Product.query.get(product_id)
    if product:
        product.quantity += quantity_change
        log_stock_movement(product_id, movement_type, abs(quantity_change), reference_type, reference_id, notes)
        db.session.commit()
        return True
    return False
