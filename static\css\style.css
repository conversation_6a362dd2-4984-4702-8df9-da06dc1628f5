/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Base Styles */
body {
    font-family: 'Noto Sans Arabic', sans-serif;
    direction: rtl;
    text-align: right;
}

/* RTL Adjustments */
.navbar-brand {
    margin-left: 0;
    margin-right: 1rem;
}

.navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Dashboard Cards */
.stats-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card .stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Warning Cards */
.warning-card {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.danger-card {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.info-card {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    vertical-align: middle;
    text-align: center;
}

/* Button Styles */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo i {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.login-title {
    text-align: center;
    color: #495057;
    font-weight: 600;
    margin-bottom: 2rem;
}

/* Barcode Styles */
.barcode-container {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    margin: 1rem 0;
}

.barcode-image {
    max-width: 100%;
    height: auto;
}

/* Product Image */
.product-image {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 10px;
}

/* Pagination */
.pagination {
    justify-content: center;
}

.page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: none;
    color: #007bff;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

/* Search Results */
.search-result-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .table {
        font-size: 10pt;
    }
    
    .table th, .table td {
        border: 1px solid #000 !important;
        padding: 0.25rem !important;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .stats-card .stats-icon {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.375rem 1rem;
        font-size: 0.875rem;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
