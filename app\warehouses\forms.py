from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, TextAreaField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length

class WarehouseForm(FlaskForm):
    name = StringField('اسم المستودع', validators=[DataRequired(), Length(min=2, max=100)],
                      render_kw={"placeholder": "أدخل اسم المستودع", "class": "form-control"})
    location = StringField('الموقع', validators=[Length(max=200)],
                          render_kw={"placeholder": "أدخل موقع المستودع", "class": "form-control"})
    description = TextAreaField('الوصف',
                               render_kw={"placeholder": "أدخل وصف المستودع", "class": "form-control", "rows": "3"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})

class CategoryForm(FlaskForm):
    name = StringField('اسم التصنيف', validators=[DataRequired(), Length(min=2, max=100)],
                      render_kw={"placeholder": "أدخل اسم التصنيف", "class": "form-control"})
    description = TextAreaField('الوصف',
                               render_kw={"placeholder": "أدخل وصف التصنيف", "class": "form-control", "rows": "3"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})

class UnitForm(FlaskForm):
    name = StringField('اسم الوحدة', validators=[DataRequired(), Length(min=1, max=50)],
                      render_kw={"placeholder": "أدخل اسم الوحدة", "class": "form-control"})
    symbol = StringField('الرمز', validators=[Length(max=10)],
                        render_kw={"placeholder": "أدخل رمز الوحدة", "class": "form-control"})
    description = TextAreaField('الوصف',
                               render_kw={"placeholder": "أدخل وصف الوحدة", "class": "form-control", "rows": "3"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})

class ConditionForm(FlaskForm):
    name = StringField('اسم الحالة', validators=[DataRequired(), Length(min=2, max=50)],
                      render_kw={"placeholder": "أدخل اسم الحالة", "class": "form-control"})
    description = TextAreaField('الوصف',
                               render_kw={"placeholder": "أدخل وصف الحالة", "class": "form-control", "rows": "3"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})

class CustomerForm(FlaskForm):
    name = StringField('اسم العميل', validators=[DataRequired(), Length(min=2, max=200)],
                      render_kw={"placeholder": "أدخل اسم العميل", "class": "form-control"})
    phone = StringField('رقم الهاتف', validators=[Length(max=50)],
                       render_kw={"placeholder": "أدخل رقم الهاتف", "class": "form-control"})
    email = StringField('البريد الإلكتروني', validators=[Length(max=120)],
                       render_kw={"placeholder": "أدخل البريد الإلكتروني", "class": "form-control"})
    address = TextAreaField('العنوان',
                           render_kw={"placeholder": "أدخل العنوان", "class": "form-control", "rows": "3"})
    tax_number = StringField('الرقم الضريبي', validators=[Length(max=50)],
                            render_kw={"placeholder": "أدخل الرقم الضريبي", "class": "form-control"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})

class SupplierForm(FlaskForm):
    name = StringField('اسم المورد', validators=[DataRequired(), Length(min=2, max=200)],
                      render_kw={"placeholder": "أدخل اسم المورد", "class": "form-control"})
    phone = StringField('رقم الهاتف', validators=[Length(max=50)],
                       render_kw={"placeholder": "أدخل رقم الهاتف", "class": "form-control"})
    email = StringField('البريد الإلكتروني', validators=[Length(max=120)],
                       render_kw={"placeholder": "أدخل البريد الإلكتروني", "class": "form-control"})
    address = TextAreaField('العنوان',
                           render_kw={"placeholder": "أدخل العنوان", "class": "form-control", "rows": "3"})
    tax_number = StringField('الرقم الضريبي', validators=[Length(max=50)],
                            render_kw={"placeholder": "أدخل الرقم الضريبي", "class": "form-control"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})
