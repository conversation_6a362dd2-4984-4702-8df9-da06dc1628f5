from flask import render_template, request
from flask_login import login_required, current_user
from app.main import bp
from app.models import Product, Warehouse, Category, InvoiceIn, InvoiceOut, User
from sqlalchemy import func
from datetime import datetime, timedelta

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    # Dashboard statistics
    stats = {}
    
    # Total products
    stats['total_products'] = Product.query.filter_by(is_active=True).count()
    
    # Total warehouses
    stats['total_warehouses'] = Warehouse.query.filter_by(is_active=True).count()
    
    # Total categories
    stats['total_categories'] = Category.query.filter_by(is_active=True).count()
    
    # Low stock products (quantity <= min_quantity)
    stats['low_stock'] = Product.query.filter(
        Product.is_active == True,
        Product.quantity <= Product.min_quantity
    ).count()
    
    # Recent invoices (last 7 days)
    week_ago = datetime.utcnow() - timedelta(days=7)
    stats['recent_invoices_in'] = InvoiceIn.query.filter(
        InvoiceIn.created_at >= week_ago
    ).count()
    stats['recent_invoices_out'] = InvoiceOut.query.filter(
        InvoiceOut.created_at >= week_ago
    ).count()
    
    # Total users
    stats['total_users'] = User.query.filter_by(is_active=True).count()
    
    # Recent products (last 10)
    recent_products = Product.query.filter_by(is_active=True).order_by(
        Product.created_at.desc()
    ).limit(10).all()
    
    # Low stock products list
    low_stock_products = Product.query.filter(
        Product.is_active == True,
        Product.quantity <= Product.min_quantity
    ).order_by(Product.quantity.asc()).limit(10).all()
    
    # Recent invoices
    recent_invoices_in = InvoiceIn.query.order_by(
        InvoiceIn.created_at.desc()
    ).limit(5).all()
    
    recent_invoices_out = InvoiceOut.query.order_by(
        InvoiceOut.created_at.desc()
    ).limit(5).all()
    
    return render_template('main/index.html', title='لوحة التحكم',
                         stats=stats,
                         recent_products=recent_products,
                         low_stock_products=low_stock_products,
                         recent_invoices_in=recent_invoices_in,
                         recent_invoices_out=recent_invoices_out)

@bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '', type=str)
    if not query:
        return render_template('main/search_results.html', title='البحث', 
                             query=query, products=[], warehouses=[], categories=[])
    
    # Search products
    products = Product.query.filter(
        Product.is_active == True,
        (Product.name.contains(query) | 
         Product.barcode.contains(query) |
         Product.description.contains(query))
    ).limit(20).all()
    
    # Search warehouses
    warehouses = Warehouse.query.filter(
        Warehouse.is_active == True,
        (Warehouse.name.contains(query) |
         Warehouse.location.contains(query) |
         Warehouse.description.contains(query))
    ).limit(10).all()
    
    # Search categories
    categories = Category.query.filter(
        Category.is_active == True,
        (Category.name.contains(query) |
         Category.description.contains(query))
    ).limit(10).all()
    
    return render_template('main/search_results.html', title='نتائج البحث',
                         query=query, products=products, 
                         warehouses=warehouses, categories=categories)
