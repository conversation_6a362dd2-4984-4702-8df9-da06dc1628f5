<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}نظام إدارة المستودعات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-warehouse me-2"></i>
                نظام إدارة المستودعات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i> المنتجات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('products.products') }}">قائمة المنتجات</a></li>
                            {% if current_user.can_edit() %}
                            <li><a class="dropdown-item" href="{{ url_for('products.add_product') }}">إضافة منتج</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('products.categories') }}">التصنيفات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('products.units') }}">وحدات القياس</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('products.conditions') }}">حالة المواد</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-warehouse"></i> المستودعات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('warehouses.warehouses') }}">قائمة المستودعات</a></li>
                            {% if current_user.can_edit() %}
                            <li><a class="dropdown-item" href="{{ url_for('warehouses.add_warehouse') }}">إضافة مستودع</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('invoices.invoices_in') }}">فواتير الإدخال</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('invoices.invoices_out') }}">فواتير الإخراج</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('invoices.production_invoices') }}">فواتير الإنتاج</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('invoices.purchase_orders') }}">طلبات الشراء</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('reports.inventory_report') }}">تقرير الجرد</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.movement_report') }}">تقرير الحركة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.low_stock_report') }}">تقرير النواقص</a></li>
                        </ul>
                    </li>
                    
                    {% if current_user.role == 'admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('settings.company_settings') }}">إعدادات الشركة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.users') }}">إدارة المستخدمين</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('settings.backup_database') }}">النسخ الاحتياطي</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3" method="GET" action="{{ url_for('main.search') }}">
                    <input class="form-control me-2" type="search" name="q" placeholder="البحث..." value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
    
    <!-- Main Content -->
    <main class="container-fluid">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام إدارة المستودعات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
