from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from datetime import datetime, timezone
from app import db
from app.settings import bp
from app.models import User, Warehouse, Category, Unit, Condition
import os
import shutil
import sqlite3
from werkzeug.security import generate_password_hash


@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('settings/index.html')


@bp.route('/users')
@login_required
def users():
    """إدارة المستخدمين"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإدارة المستخدمين', 'error')
        return redirect(url_for('main.index'))
    
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role = request.args.get('role', '')
    
    query = User.query
    
    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.full_name.contains(search),
                User.email.contains(search)
            )
        )
    
    if role:
        query = query.filter_by(role=role)
    
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Calculate statistics
    total_users = User.query.count()
    admin_users = User.query.filter_by(role='admin').count()
    employee_users = User.query.filter_by(role='employee').count()
    viewer_users = User.query.filter_by(role='viewer').count()
    active_users = User.query.filter_by(is_active=True).count()
    
    return render_template('settings/users.html',
                         users=users,
                         total_users=total_users,
                         admin_users=admin_users,
                         employee_users=employee_users,
                         viewer_users=viewer_users,
                         active_users=active_users)


@bp.route('/add_user', methods=['GET', 'POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإضافة المستخدمين', 'error')
        return redirect(url_for('settings.users'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        role = request.form.get('role')
        is_active = request.form.get('is_active') == 'on'
        
        # Validate input
        if not username or not password or not full_name or not role:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
            return render_template('settings/user_form.html', action='add')
        
        # Check if username already exists
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('settings/user_form.html', action='add')
        
        # Check if email already exists
        if email and User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('settings/user_form.html', action='add')
        
        try:
            user = User(
                username=username,
                password_hash=generate_password_hash(password),
                full_name=full_name,
                email=email,
                role=role,
                is_active=is_active,
                created_at=datetime.now(timezone.utc),
                created_by=current_user.id
            )
            
            db.session.add(user)
            db.session.commit()
            flash(f'تم إضافة المستخدم {user.full_name} بنجاح', 'success')
            return redirect(url_for('settings.users'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة المستخدم', 'error')
    
    return render_template('settings/user_form.html', action='add')


@bp.route('/edit_user/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    """تعديل مستخدم"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('settings.users'))
    
    user = User.query.get_or_404(id)
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        role = request.form.get('role')
        is_active = request.form.get('is_active') == 'on'
        
        # Validate input
        if not username or not full_name or not role:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
            return render_template('settings/user_form.html', action='edit', user=user)
        
        # Check if username already exists (excluding current user)
        existing_user = User.query.filter_by(username=username).first()
        if existing_user and existing_user.id != user.id:
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('settings/user_form.html', action='edit', user=user)
        
        # Check if email already exists (excluding current user)
        if email:
            existing_user = User.query.filter_by(email=email).first()
            if existing_user and existing_user.id != user.id:
                flash('البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('settings/user_form.html', action='edit', user=user)
        
        try:
            user.username = username
            user.full_name = full_name
            user.email = email
            user.role = role
            user.is_active = is_active
            
            # Update password if provided
            if password:
                user.password_hash = generate_password_hash(password)
            
            db.session.commit()
            flash(f'تم تحديث بيانات المستخدم {user.full_name} بنجاح', 'success')
            return redirect(url_for('settings.users'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث المستخدم', 'error')
    
    return render_template('settings/user_form.html', action='edit', user=user)


@bp.route('/delete_user/<int:id>', methods=['POST'])
@login_required
def delete_user(id):
    """حذف مستخدم"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لحذف المستخدمين', 'error')
        return redirect(url_for('settings.users'))
    
    user = User.query.get_or_404(id)
    
    # Prevent deleting self
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('settings.users'))
    
    # Prevent deleting the last admin
    if user.role == 'admin':
        admin_count = User.query.filter_by(role='admin').count()
        if admin_count <= 1:
            flash('لا يمكن حذف آخر مدير في النظام', 'error')
            return redirect(url_for('settings.users'))
    
    try:
        db.session.delete(user)
        db.session.commit()
        flash(f'تم حذف المستخدم {user.full_name} بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف المستخدم', 'error')
    
    return redirect(url_for('settings.users'))


@bp.route('/company_settings', methods=['GET', 'POST'])
@login_required
def company_settings():
    """إعدادات الشركة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل إعدادات الشركة', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        # This would typically save to a settings table or configuration file
        company_name = request.form.get('company_name')
        company_address = request.form.get('company_address')
        company_phone = request.form.get('company_phone')
        company_email = request.form.get('company_email')
        tax_number = request.form.get('tax_number')
        currency = request.form.get('currency')
        
        # Save settings (implement based on your preference)
        # For now, we'll just show a success message
        flash('تم حفظ إعدادات الشركة بنجاح', 'success')
    
    # Load current settings (implement based on your preference)
    settings = {
        'company_name': 'شركة إدارة المستودعات',
        'company_address': '',
        'company_phone': '',
        'company_email': '',
        'tax_number': '',
        'currency': 'SAR'
    }
    
    return render_template('settings/company_settings.html', settings=settings)


@bp.route('/system_settings', methods=['GET', 'POST'])
@login_required
def system_settings():
    """إعدادات النظام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل إعدادات النظام', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        # System settings
        default_warehouse = request.form.get('default_warehouse')
        auto_generate_barcode = request.form.get('auto_generate_barcode') == 'on'
        low_stock_threshold = request.form.get('low_stock_threshold', type=int)
        backup_frequency = request.form.get('backup_frequency')
        
        # Save settings
        flash('تم حفظ إعدادات النظام بنجاح', 'success')
    
    # Load current settings
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    
    settings = {
        'default_warehouse': '',
        'auto_generate_barcode': True,
        'low_stock_threshold': 10,
        'backup_frequency': 'daily'
    }
    
    return render_template('settings/system_settings.html', 
                         settings=settings, 
                         warehouses=warehouses)


@bp.route('/backup_database')
@login_required
def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإنشاء النسخ الاحتياطية', 'error')
        return redirect(url_for('main.index'))
    
    try:
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(current_app.root_path, '..', 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'warehouse_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Get database path
        db_path = current_app.config.get('DATABASE_PATH', 'warehouse.db')
        
        # Copy database file
        shutil.copy2(db_path, backup_path)
        
        flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))


@bp.route('/restore_database', methods=['POST'])
@login_required
def restore_database():
    """استعادة قاعدة البيانات من نسخة احتياطية"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لاستعادة النسخ الاحتياطية', 'error')
        return redirect(url_for('main.index'))
    
    backup_file = request.files.get('backup_file')
    
    if not backup_file or backup_file.filename == '':
        flash('يرجى اختيار ملف النسخة الاحتياطية', 'error')
        return redirect(url_for('settings.index'))
    
    try:
        # Validate file extension
        if not backup_file.filename.endswith('.db'):
            flash('نوع الملف غير صحيح. يجب أن يكون ملف قاعدة بيانات (.db)', 'error')
            return redirect(url_for('settings.index'))
        
        # Save uploaded file temporarily
        temp_path = os.path.join(current_app.root_path, '..', 'temp_restore.db')
        backup_file.save(temp_path)
        
        # Validate database file
        try:
            conn = sqlite3.connect(temp_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            
            # Check if essential tables exist
            table_names = [table[0] for table in tables]
            required_tables = ['user', 'warehouse', 'product', 'category']
            
            if not all(table in table_names for table in required_tables):
                flash('ملف النسخة الاحتياطية غير صحيح أو تالف', 'error')
                os.remove(temp_path)
                return redirect(url_for('settings.index'))
                
        except sqlite3.Error:
            flash('ملف النسخة الاحتياطية غير صحيح أو تالف', 'error')
            os.remove(temp_path)
            return redirect(url_for('settings.index'))
        
        # Create backup of current database
        current_db_path = current_app.config.get('DATABASE_PATH', 'warehouse.db')
        backup_current = f'warehouse_backup_before_restore_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        backup_current_path = os.path.join(current_app.root_path, '..', 'backups', backup_current)
        
        os.makedirs(os.path.dirname(backup_current_path), exist_ok=True)
        shutil.copy2(current_db_path, backup_current_path)
        
        # Replace current database with restored one
        shutil.copy2(temp_path, current_db_path)
        os.remove(temp_path)
        
        flash(f'تم استعادة قاعدة البيانات بنجاح. تم حفظ النسخة الحالية كـ: {backup_current}', 'success')
        flash('يرجى إعادة تسجيل الدخول', 'info')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة قاعدة البيانات: {str(e)}', 'error')
        # Clean up temp file if it exists
        temp_path = os.path.join(current_app.root_path, '..', 'temp_restore.db')
        if os.path.exists(temp_path):
            os.remove(temp_path)
    
    return redirect(url_for('settings.index'))


@bp.route('/system_info')
@login_required
def system_info():
    """معلومات النظام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لعرض معلومات النظام', 'error')
        return redirect(url_for('main.index'))
    
    # Gather system information
    info = {
        'database_size': get_database_size(),
        'total_users': User.query.count(),
        'total_warehouses': Warehouse.query.count(),
        'total_categories': Category.query.count(),
        'total_units': Unit.query.count(),
        'total_conditions': Condition.query.count(),
        'system_version': '1.0.0',
        'python_version': '3.x',
        'flask_version': 'Flask 2.x'
    }
    
    return render_template('settings/system_info.html', info=info)


def get_database_size():
    """حساب حجم قاعدة البيانات"""
    try:
        db_path = current_app.config.get('DATABASE_PATH', 'warehouse.db')
        if os.path.exists(db_path):
            size_bytes = os.path.getsize(db_path)
            # Convert to MB
            size_mb = size_bytes / (1024 * 1024)
            return f"{size_mb:.2f} MB"
        return "غير معروف"
    except:
        return "غير معروف"


@bp.route('/clear_logs', methods=['POST'])
@login_required
def clear_logs():
    """مسح سجلات النظام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لمسح السجلات', 'error')
        return redirect(url_for('settings.index'))
    
    try:
        # Clear log files if they exist
        log_dir = os.path.join(current_app.root_path, '..', 'logs')
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    os.remove(os.path.join(log_dir, filename))
        
        flash('تم مسح سجلات النظام بنجاح', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء مسح السجلات: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))


@bp.route('/optimize_database', methods=['POST'])
@login_required
def optimize_database():
    """تحسين قاعدة البيانات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتحسين قاعدة البيانات', 'error')
        return redirect(url_for('settings.index'))
    
    try:
        # Run VACUUM command to optimize database
        db.session.execute('VACUUM')
        db.session.commit()
        
        flash('تم تحسين قاعدة البيانات بنجاح', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء تحسين قاعدة البيانات: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))
