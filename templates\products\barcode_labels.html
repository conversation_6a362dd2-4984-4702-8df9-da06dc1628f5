{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-barcode me-2"></i>
            طباعة ملصقات الباركود
        </h1>
        <p class="text-muted">إنشاء وطباعة ملصقات الباركود بتنسيق A4 (5 ملصقات في الصف)</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('products.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمنتجات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات الطباعة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('products.barcode_labels') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.product_id.label(class="form-label required") }}
                        {{ form.product_id(class="form-select" + (" is-invalid" if form.product_id.errors else "")) }}
                        {% if form.product_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.product_id.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اختر المنتج المراد طباعة ملصقاته</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.copies.label(class="form-label required") }}
                        {{ form.copies(class="form-control" + (" is-invalid" if form.copies.errors else "")) }}
                        {% if form.copies.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.copies.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">عدد الملصقات المراد طباعتها (الحد الأقصى: 100)</div>
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطباعة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-print me-2"></i>
                        مواصفات الطباعة
                    </h6>
                    <ul class="mb-0">
                        <li>حجم الورق: A4</li>
                        <li>عدد الملصقات في الصف: 5</li>
                        <li>عدد الصفوف في الصفحة: 13</li>
                        <li>إجمالي الملصقات في الصفحة: 65</li>
                        <li>أبعاد الملصق: 38.1 × 21.2 مم</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تعليمات الطباعة
                    </h6>
                    <ul class="mb-0">
                        <li>استخدم ورق ملصقات A4 قياسي</li>
                        <li>تأكد من إعدادات الطابعة على "بدون هوامش"</li>
                        <li>اختر جودة طباعة عالية للباركود</li>
                        <li>تأكد من محاذاة الورق بشكل صحيح</li>
                    </ul>
                </div>
                
                <div class="text-center">
                    <img src="{{ url_for('static', filename='images/barcode-sample.png') }}" 
                         alt="نموذج ملصق الباركود" class="img-fluid border rounded" 
                         style="max-width: 200px;" onerror="this.style.display='none'">
                    <p class="text-muted mt-2 mb-0">نموذج ملصق الباركود</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Labels -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    الملصقات المطبوعة مؤخراً
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for product in recent_products[:8] %}
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center p-3">
                                <div class="mb-2">
                                    <div style="background: repeating-linear-gradient(90deg, #000 0px, #000 1px, #fff 1px, #fff 2px); height: 30px; border: 1px solid #ddd;"></div>
                                </div>
                                <h6 class="card-title mb-1">{{ product.name[:20] }}{% if product.name|length > 20 %}...{% endif %}</h6>
                                <p class="card-text mb-2">
                                    <code class="small">{{ product.barcode }}</code>
                                </p>
                                <button onclick="printSingleLabel('{{ product.id }}')" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة سريعة
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if not recent_products %}
                <div class="text-center py-3">
                    <i class="fas fa-barcode fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد منتجات لطباعة الملصقات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Print single label for quick printing
function printSingleLabel(productId) {
    // Create a form and submit it to generate single label
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("products.barcode_labels") }}';
    form.target = '_blank';
    
    // Add CSRF token
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);
    
    // Add product ID
    const productInput = document.createElement('input');
    productInput.type = 'hidden';
    productInput.name = 'product_id';
    productInput.value = productId;
    form.appendChild(productInput);
    
    // Add copies (1 for quick print)
    const copiesInput = document.createElement('input');
    copiesInput.type = 'hidden';
    copiesInput.name = 'copies';
    copiesInput.value = '1';
    form.appendChild(copiesInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// Auto-update preview when product changes
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product_id');
    if (productSelect) {
        productSelect.addEventListener('change', function() {
            updateLabelPreview(this.value);
        });
    }
});

function updateLabelPreview(productId) {
    if (!productId) return;
    
    // Fetch product details for preview
    fetch(`/products/api/get-by-barcode-id/${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update preview (if preview area exists)
                const previewArea = document.getElementById('label-preview');
                if (previewArea) {
                    previewArea.innerHTML = `
                        <div class="border p-2 text-center" style="width: 150px; margin: 0 auto;">
                            <div style="background: repeating-linear-gradient(90deg, #000 0px, #000 1px, #fff 1px, #fff 2px); height: 40px; border: 1px solid #ddd; margin-bottom: 5px;"></div>
                            <div class="small fw-bold">${data.product.name}</div>
                            <div class="small"><code>${data.product.barcode}</code></div>
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}
</script>
{% endblock %}
