// Main JavaScript file for Warehouse Management System

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Confirm delete actions
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// Generate random barcode
function generateBarcode() {
    const barcodeField = document.getElementById('barcode');
    if (barcodeField) {
        const randomBarcode = Math.floor(Math.random() * 9000000000000) + 1000000000000;
        barcodeField.value = randomBarcode.toString();
    }
}

// Auto-fill product details when barcode is entered
function autoFillProduct(barcodeValue, targetForm = 'invoice-form') {
    if (barcodeValue.length >= 10) {
        fetch(`/products/api/get-by-barcode/${barcodeValue}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const product = data.product;
                    
                    // Fill product details in form
                    const productNameField = document.querySelector(`#${targetForm} [name="product_name"]`);
                    const unitField = document.querySelector(`#${targetForm} [name="unit"]`);
                    const categoryField = document.querySelector(`#${targetForm} [name="category"]`);
                    const conditionField = document.querySelector(`#${targetForm} [name="condition"]`);
                    const warehouseField = document.querySelector(`#${targetForm} [name="warehouse"]`);
                    const availableQtyField = document.querySelector(`#${targetForm} [name="available_quantity"]`);
                    
                    if (productNameField) productNameField.value = product.name;
                    if (unitField) unitField.value = product.unit;
                    if (categoryField) categoryField.value = product.category;
                    if (conditionField) conditionField.value = product.condition;
                    if (warehouseField) warehouseField.value = product.warehouse;
                    if (availableQtyField) availableQtyField.value = product.quantity;
                    
                    // Show success message
                    showAlert('تم العثور على المنتج وتعبئة البيانات تلقائياً', 'success');
                } else {
                    // Show add new product button
                    showAddNewProductOption(barcodeValue);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ في البحث عن المنتج', 'danger');
            });
    }
}

// Show add new product option
function showAddNewProductOption(barcode) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-warning alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>لم يتم العثور على المنتج!</strong>
        <br>الباركود: ${barcode}
        <br>
        <button type="button" class="btn btn-primary btn-sm mt-2" onclick="addNewProduct('${barcode}')">
            إضافة منتج جديد
        </button>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert alert at the top of the form
    const form = document.querySelector('.invoice-form, .product-form');
    if (form) {
        form.insertBefore(alertDiv, form.firstChild);
    }
}

// Add new product with pre-filled barcode
function addNewProduct(barcode) {
    const url = `/products/add?barcode=${encodeURIComponent(barcode)}`;
    window.open(url, '_blank');
}

// Show alert messages
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the page
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Print function
function printPage() {
    window.print();
}

// Export to Excel
function exportToExcel(tableId, filename = 'export') {
    const table = document.getElementById(tableId);
    if (!table) {
        showAlert('لم يتم العثور على الجدول للتصدير', 'danger');
        return;
    }
    
    // Create a simple CSV export (can be enhanced with a proper Excel library)
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            rowData.push('"' + col.textContent.replace(/"/g, '""') + '"');
        });
        csv += rowData.join(',') + '\n';
    });
    
    // Download CSV file
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename + '.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Calculate invoice totals
function calculateInvoiceTotal() {
    const quantityInputs = document.querySelectorAll('[name*="quantity"]');
    const priceInputs = document.querySelectorAll('[name*="unit_price"]');
    const totalInputs = document.querySelectorAll('[name*="total_price"]');
    
    let grandTotal = 0;
    
    quantityInputs.forEach((qtyInput, index) => {
        const priceInput = priceInputs[index];
        const totalInput = totalInputs[index];
        
        if (qtyInput && priceInput && totalInput) {
            const quantity = parseFloat(qtyInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = quantity * price;
            
            totalInput.value = total.toFixed(2);
            grandTotal += total;
        }
    });
    
    const grandTotalField = document.getElementById('grand_total');
    if (grandTotalField) {
        grandTotalField.textContent = grandTotal.toFixed(2);
    }
}

// Add event listeners for invoice calculations
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('[name*="quantity"]');
    const priceInputs = document.querySelectorAll('[name*="unit_price"]');
    
    quantityInputs.forEach(input => {
        input.addEventListener('input', calculateInvoiceTotal);
    });
    
    priceInputs.forEach(input => {
        input.addEventListener('input', calculateInvoiceTotal);
    });
    
    // Initial calculation
    calculateInvoiceTotal();
});

// Barcode scanning simulation (can be enhanced with real barcode scanner)
function simulateBarcodeScan() {
    const barcodeInput = document.querySelector('[name="barcode"]');
    if (barcodeInput) {
        barcodeInput.focus();
        barcodeInput.select();
    }
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+B for barcode scan
    if (e.ctrlKey && e.key === 'b') {
        e.preventDefault();
        simulateBarcodeScan();
    }
    
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printPage();
    }
    
    // F1 for help (can be enhanced)
    if (e.key === 'F1') {
        e.preventDefault();
        showAlert('اختصارات لوحة المفاتيح: Ctrl+B للباركود، Ctrl+P للطباعة', 'info');
    }
});

// Form validation helpers
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
    }
    
    return isValid;
}

// Auto-save draft functionality
function autoSaveDraft(formId) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    const formData = new FormData(form);
    const draftData = {};
    
    for (let [key, value] of formData.entries()) {
        draftData[key] = value;
    }
    
    localStorage.setItem(`draft_${formId}`, JSON.stringify(draftData));
}

// Load draft functionality
function loadDraft(formId) {
    const draftData = localStorage.getItem(`draft_${formId}`);
    if (!draftData) return;
    
    const data = JSON.parse(draftData);
    const form = document.getElementById(formId);
    if (!form) return;
    
    Object.keys(data).forEach(key => {
        const field = form.querySelector(`[name="${key}"]`);
        if (field) {
            field.value = data[key];
        }
    });
    
    showAlert('تم استرداد المسودة المحفوظة', 'info');
}

// Clear draft
function clearDraft(formId) {
    localStorage.removeItem(`draft_${formId}`);
}

// Initialize auto-save for forms
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[data-auto-save]');
    forms.forEach(form => {
        const formId = form.id;
        if (formId) {
            // Load draft on page load
            loadDraft(formId);
            
            // Auto-save every 30 seconds
            setInterval(() => {
                autoSaveDraft(formId);
            }, 30000);
            
            // Save on form change
            form.addEventListener('change', () => {
                autoSaveDraft(formId);
            });
            
            // Clear draft on successful submit
            form.addEventListener('submit', () => {
                setTimeout(() => {
                    clearDraft(formId);
                }, 1000);
            });
        }
    });
});
