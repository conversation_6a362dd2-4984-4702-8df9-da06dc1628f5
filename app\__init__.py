import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from datetime import datetime, timezone

db = SQLAlchemy()
login_manager = LoginManager()

def create_app():
    # Get the absolute path to the templates directory
    basedir = os.path.abspath(os.path.dirname(__file__))
    template_dir = os.path.join(os.path.dirname(basedir), 'templates')
    static_dir = os.path.join(os.path.dirname(basedir), 'static')

    app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
    
    # Configuration
    app.config['SECRET_KEY'] = 'warehouse-management-system-2025'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = os.path.join(app.instance_path, 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    
    # Ensure instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass
    
    # Ensure upload folder exists
    try:
        os.makedirs(app.config['UPLOAD_FOLDER'])
    except OSError:
        pass
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    # Import models
    from app.models import User, Product, Warehouse, Category, Unit, Condition, Customer, Supplier
    from app.models import InvoiceIn, InvoiceOut, ProductionInvoice, PurchaseOrder
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.products import bp as products_bp
    app.register_blueprint(products_bp, url_prefix='/products')
    
    from app.warehouses import bp as warehouses_bp
    app.register_blueprint(warehouses_bp, url_prefix='/warehouses')
    
    from app.invoices import bp as invoices_bp
    app.register_blueprint(invoices_bp, url_prefix='/invoices')
    
    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    from app.settings import bp as settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')

    # Register template context processors
    @app.context_processor
    def utility_processor():
        from app.utils import create_barcode_image, format_currency
        return dict(create_barcode_image=create_barcode_image, format_currency=format_currency)

    # Create database tables
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            from werkzeug.security import generate_password_hash
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                full_name='مدير النظام',
                created_at=datetime.now(timezone.utc)
            )
            db.session.add(admin)
            db.session.commit()

    return app
