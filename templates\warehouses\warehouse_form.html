{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-{% if action == 'add' %}plus{% else %}edit{% endif %} me-2"></i>
            {% if action == 'add' %}إضافة مستودع جديد{% else %}تعديل المستودع{% endif %}
        </h1>
        {% if action == 'edit' %}
        <p class="text-muted">تعديل بيانات المستودع: {{ warehouse.name }}</p>
        {% endif %}
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('warehouses.warehouses') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    بيانات المستودع
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="warehouse-form">
                    {{ form.hidden_tag() }}
                    
                    <!-- Warehouse Name -->
                    <div class="mb-3">
                        {{ form.name.label(class="form-label required") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- Location -->
                        <div class="col-md-6 mb-3">
                            {{ form.location.label(class="form-label") }}
                            {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                            {% if form.location.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.location.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Manager -->
                        <div class="col-md-6 mb-3">
                            {{ form.manager.label(class="form-label") }}
                            {{ form.manager(class="form-control" + (" is-invalid" if form.manager.errors else "")) }}
                            {% if form.manager.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.manager.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Notes -->
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Active Status -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input" + (" is-invalid" if form.is_active.errors else "")) }}
                            {{ form.is_active.label(class="form-check-label") }}
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-success btn-lg") }}
                            <a href="{{ url_for('warehouses.warehouses') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        {% if action == 'edit' %}
                        <div>
                            <a href="{{ url_for('warehouses.warehouse_detail', id=warehouse.id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Statistics (for edit mode) -->
        {% if action == 'edit' %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات المستودع
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-1 text-primary">{{ warehouse.products|length }}</div>
                            <small class="text-muted">عدد المنتجات</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-1 text-success">{{ warehouse.total_quantity or 0 }}</div>
                            <small class="text-muted">إجمالي الكمية</small>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        تم الإنشاء: {{ warehouse.created_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% if warehouse.updated_at %}
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-edit me-1"></i>
                        آخر تحديث: {{ warehouse.updated_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Help -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>اسم المستودع:</strong> يجب أن يكون فريداً ووصفياً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-map-marker-alt text-warning me-2"></i>
                        <strong>الموقع:</strong> العنوان الفيزيائي للمستودع
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-user text-success me-2"></i>
                        <strong>المسؤول:</strong> الشخص المسؤول عن إدارة المستودع
                    </li>
                    <li>
                        <i class="fas fa-toggle-on text-primary me-2"></i>
                        <strong>الحالة:</strong> المستودعات غير النشطة لا تظهر في القوائم
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Activity (for edit mode) -->
        {% if action == 'edit' and warehouse.recent_movements %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for movement in warehouse.recent_movements[:5] %}
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ movement.product_ref.name }}</div>
                                <small class="text-muted">{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <span class="badge bg-{% if movement.movement_type == 'in' %}success{% else %}danger{% endif %} rounded-pill">
                                {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('warehouse-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm('warehouse-form')) {
                e.preventDefault();
            }
        });
    }
    
    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Remove non-digits
            let value = e.target.value.replace(/\D/g, '');
            
            // Format as phone number
            if (value.length >= 10) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
            }
            
            e.target.value = value;
        });
    }
});
</script>
{% endblock %}
