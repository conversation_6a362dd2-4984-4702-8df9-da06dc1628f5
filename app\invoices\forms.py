from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, DecimalField, IntegerField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import date

def safe_int_coerce(value):
    """Safely coerce value to int, return None for empty strings"""
    if value == '' or value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None


class InvoiceInForm(FlaskForm):
    """نموذج فاتورة الشراء"""
    invoice_number = StringField('رقم الفاتورة', validators=[DataRequired(), Length(max=50)])
    reference_number = StringField('الرقم المرجعي', validators=[Optional(), Length(max=50)])
    invoice_date = DateField('تاريخ الفاتورة', validators=[DataRequired()], default=date.today)
    supplier_id = SelectField('المورد', validators=[DataRequired()], coerce=int)
    warehouse_id = SelectField('المستودع', validators=[DataRequired()], coerce=int)
    status = SelectField('الحالة', choices=[
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكدة'),
        ('received', 'مستلمة')
    ], default='draft')
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ الفاتورة')


class InvoiceOutForm(FlaskForm):
    """نموذج فاتورة البيع"""
    invoice_number = StringField('رقم الفاتورة', validators=[DataRequired(), Length(max=50)])
    reference_number = StringField('الرقم المرجعي', validators=[Optional(), Length(max=50)])
    invoice_date = DateField('تاريخ الفاتورة', validators=[DataRequired()], default=date.today)
    customer_id = SelectField('العميل', validators=[DataRequired()], coerce=int)
    warehouse_id = SelectField('المستودع', validators=[DataRequired()], coerce=int)
    status = SelectField('الحالة', choices=[
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكدة'),
        ('delivered', 'مسلمة')
    ], default='draft')
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ الفاتورة')


class InvoiceItemForm(FlaskForm):
    """نموذج صنف الفاتورة"""
    product_id = SelectField('المنتج', validators=[DataRequired()], coerce=int)
    quantity = DecimalField('الكمية', validators=[DataRequired(), NumberRange(min=0.01)], places=2)
    unit_price = DecimalField('سعر الوحدة', validators=[DataRequired(), NumberRange(min=0)], places=2)
    notes = StringField('ملاحظات', validators=[Optional(), Length(max=200)])


class ProductionInvoiceForm(FlaskForm):
    """نموذج فاتورة الإنتاج"""
    invoice_number = StringField('رقم فاتورة الإنتاج', validators=[DataRequired(), Length(max=50)])
    production_date = DateField('تاريخ الإنتاج', validators=[DataRequired()], default=date.today)
    warehouse_id = SelectField('المستودع', validators=[DataRequired()], coerce=int)
    finished_product_id = SelectField('المنتج النهائي', validators=[DataRequired()], coerce=int)
    finished_quantity = DecimalField('كمية الإنتاج', validators=[DataRequired(), NumberRange(min=0.01)], places=2)
    production_cost = DecimalField('تكلفة الإنتاج', validators=[Optional(), NumberRange(min=0)], places=2)
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ فاتورة الإنتاج')


class ProductionItemForm(FlaskForm):
    """نموذج مادة خام في فاتورة الإنتاج"""
    raw_material_id = SelectField('المادة الخام', validators=[DataRequired()], coerce=int)
    quantity_used = DecimalField('الكمية المستخدمة', validators=[DataRequired(), NumberRange(min=0.01)], places=2)
    unit_cost = DecimalField('تكلفة الوحدة', validators=[Optional(), NumberRange(min=0)], places=2)
    notes = StringField('ملاحظات', validators=[Optional(), Length(max=200)])


class BarcodeSearchForm(FlaskForm):
    """نموذج البحث بالباركود"""
    barcode = StringField('الباركود', validators=[DataRequired(), Length(max=50)])
    submit = SubmitField('بحث')


class QuickAddProductForm(FlaskForm):
    """نموذج إضافة منتج سريع من الفاتورة"""
    name = StringField('اسم المنتج', validators=[DataRequired(), Length(max=200)])
    barcode = StringField('الباركود', validators=[DataRequired(), Length(max=50)])
    category_id = SelectField('التصنيف', validators=[DataRequired()], coerce=int)
    unit_id = SelectField('وحدة القياس', validators=[DataRequired()], coerce=int)
    condition_id = SelectField('حالة المادة', validators=[DataRequired()], coerce=int)
    product_type = SelectField('نوع المنتج', choices=[
        ('raw_material', 'مادة خام'),
        ('finished_product', 'منتج نهائي')
    ], default='raw_material')
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    submit = SubmitField('إضافة المنتج')


class InvoiceFilterForm(FlaskForm):
    """نموذج تصفية الفواتير"""
    search = StringField('البحث', validators=[Optional(), Length(max=100)])
    supplier_id = SelectField('المورد', validators=[Optional()], coerce=int)
    customer_id = SelectField('العميل', validators=[Optional()], coerce=int)
    warehouse_id = SelectField('المستودع', validators=[Optional()], coerce=int)
    status = SelectField('الحالة', validators=[Optional()])
    date_from = DateField('من تاريخ', validators=[Optional()])
    date_to = DateField('إلى تاريخ', validators=[Optional()])
    submit = SubmitField('تصفية')


class BulkInvoiceActionForm(FlaskForm):
    """نموذج الإجراءات المجمعة على الفواتير"""
    action = SelectField('الإجراء', choices=[
        ('confirm', 'تأكيد'),
        ('cancel', 'إلغاء'),
        ('delete', 'حذف')
    ], validators=[DataRequired()])
    invoice_ids = StringField('معرفات الفواتير', validators=[DataRequired()])
    submit = SubmitField('تنفيذ')


class InvoiceReportForm(FlaskForm):
    """نموذج تقرير الفواتير"""
    report_type = SelectField('نوع التقرير', choices=[
        ('summary', 'ملخص'),
        ('detailed', 'تفصيلي'),
        ('by_supplier', 'حسب المورد'),
        ('by_customer', 'حسب العميل'),
        ('by_product', 'حسب المنتج')
    ], validators=[DataRequired()])
    date_from = DateField('من تاريخ', validators=[DataRequired()])
    date_to = DateField('إلى تاريخ', validators=[DataRequired()])
    supplier_id = SelectField('المورد', validators=[Optional()], coerce=int)
    customer_id = SelectField('العميل', validators=[Optional()], coerce=int)
    warehouse_id = SelectField('المستودع', validators=[Optional()], coerce=int)
    status = SelectField('الحالة', validators=[Optional()])
    export_format = SelectField('تنسيق التصدير', choices=[
        ('html', 'عرض على الشاشة'),
        ('pdf', 'PDF'),
        ('excel', 'Excel')
    ], default='html')
    submit = SubmitField('إنشاء التقرير')


class StockAdjustmentForm(FlaskForm):
    """نموذج تعديل المخزون"""
    product_id = SelectField('المنتج', validators=[DataRequired()], coerce=int)
    warehouse_id = SelectField('المستودع', validators=[DataRequired()], coerce=int)
    adjustment_type = SelectField('نوع التعديل', choices=[
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('set', 'تحديد الكمية')
    ], validators=[DataRequired()])
    quantity = DecimalField('الكمية', validators=[DataRequired(), NumberRange(min=0)], places=2)
    reason = SelectField('السبب', choices=[
        ('damaged', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
        ('lost', 'مفقود'),
        ('found', 'موجود'),
        ('correction', 'تصحيح'),
        ('other', 'أخرى')
    ], validators=[DataRequired()])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('تطبيق التعديل')


class TransferForm(FlaskForm):
    """نموذج نقل البضائع بين المستودعات"""
    transfer_number = StringField('رقم النقل', validators=[DataRequired(), Length(max=50)])
    transfer_date = DateField('تاريخ النقل', validators=[DataRequired()], default=date.today)
    from_warehouse_id = SelectField('من مستودع', validators=[DataRequired()], coerce=int)
    to_warehouse_id = SelectField('إلى مستودع', validators=[DataRequired()], coerce=int)
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ النقل')


class TransferItemForm(FlaskForm):
    """نموذج صنف النقل"""
    product_id = SelectField('المنتج', validators=[DataRequired()], coerce=int)
    quantity = DecimalField('الكمية', validators=[DataRequired(), NumberRange(min=0.01)], places=2)
    notes = StringField('ملاحظات', validators=[Optional(), Length(max=200)])
