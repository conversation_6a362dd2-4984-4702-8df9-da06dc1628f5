{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-box me-2"></i>
            {{ product.name }}
        </h1>
        <p class="text-muted">
            <code>{{ product.barcode }}</code>
            <span class="badge bg-{% if product.product_type == 'raw' %}info{% else %}success{% endif %} ms-2">
                {% if product.product_type == 'raw' %}مادة خام{% else %}منتج نهائي{% endif %}
            </span>
            {% if not product.is_active %}
            <span class="badge bg-danger ms-2">غير نشط</span>
            {% endif %}
        </p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            <a href="{{ url_for('products.products') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('products.edit_product', id=product.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <!-- Product Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المنتج
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">الباركود:</td>
                                <td><code class="fs-6">{{ product.barcode }}</code></td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">اسم المنتج:</td>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">التصنيف:</td>
                                <td>
                                    <a href="{{ url_for('products.category_products', id=product.category_id) }}" class="text-decoration-none">
                                        {{ product.category_ref.name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">وحدة القياس:</td>
                                <td>{{ product.unit_ref.name }}{% if product.unit_ref.symbol %} ({{ product.unit_ref.symbol }}){% endif %}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">حالة المادة:</td>
                                <td>{{ product.condition_ref.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">المستودع:</td>
                                <td>
                                    <a href="{{ url_for('warehouses.warehouse_products', id=product.warehouse_id) }}" class="text-decoration-none">
                                        {{ product.warehouse_ref.name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الكمية الحالية:</td>
                                <td>
                                    <span class="{% if product.quantity <= product.min_quantity %}text-warning fw-bold{% endif %}">
                                        {{ product.quantity }} {{ product.unit_ref.name }}
                                    </span>
                                    {% if product.quantity <= product.min_quantity %}
                                    <br><small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        أقل من الحد الأدنى
                                    </small>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الحد الأدنى:</td>
                                <td>{{ product.min_quantity }} {{ product.unit_ref.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">نوع المنتج:</td>
                                <td>
                                    <span class="badge bg-{% if product.product_type == 'raw' %}info{% else %}success{% endif %}">
                                        {% if product.product_type == 'raw' %}مادة خام{% else %}منتج نهائي{% endif %}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الحالة:</td>
                                <td>
                                    <span class="badge bg-{% if product.is_active %}success{% else %}danger{% endif %}">
                                        {% if product.is_active %}نشط{% else %}غير نشط{% endif %}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if product.description %}
                <div class="mt-3">
                    <h6 class="fw-bold text-muted">الوصف:</h6>
                    <p class="mb-0">{{ product.description }}</p>
                </div>
                {% endif %}
                
                {% if product.notes %}
                <div class="mt-3">
                    <h6 class="fw-bold text-muted">ملاحظات:</h6>
                    <p class="mb-0">{{ product.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Stock Movement History -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ حركة المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if product.stock_movements %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>الرصيد بعد الحركة</th>
                                <th>المرجع</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in product.stock_movements[:10] %}
                            <tr>
                                <td>{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge bg-{% if movement.movement_type == 'in' %}success{% else %}danger{% endif %}">
                                        {% if movement.movement_type == 'in' %}إدخال{% else %}إخراج{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="{% if movement.movement_type == 'in' %}text-success{% else %}text-danger{% endif %}">
                                        {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                                    </span>
                                </td>
                                <td>{{ movement.balance_after }}</td>
                                <td>
                                    {% if movement.reference_type == 'invoice_in' %}
                                    <a href="{{ url_for('invoices.invoice_in_detail', id=movement.reference_id) }}">
                                        فاتورة إدخال #{{ movement.reference_id }}
                                    </a>
                                    {% elif movement.reference_type == 'invoice_out' %}
                                    <a href="{{ url_for('invoices.invoice_out_detail', id=movement.reference_id) }}">
                                        فاتورة إخراج #{{ movement.reference_id }}
                                    </a>
                                    {% else %}
                                    {{ movement.reference_type }}
                                    {% endif %}
                                </td>
                                <td>{{ movement.user_ref.full_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if product.stock_movements|length > 10 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('reports.product_movement_report', product_id=product.id) }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الحركات
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد حركات مخزون لهذا المنتج</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Product Image -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>
                    صورة المنتج
                </h5>
            </div>
            <div class="card-body text-center">
                {% if product.image_path %}
                <img src="{{ url_for('static', filename='uploads/products/' + product.image_path) }}" 
                     alt="{{ product.name }}" class="img-fluid rounded" style="max-height: 250px;">
                {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                    <i class="fas fa-box fa-3x text-muted"></i>
                </div>
                <p class="text-muted mt-2 mb-0">لا توجد صورة</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Barcode -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-barcode me-2"></i>
                    الباركود
                </h5>
            </div>
            <div class="card-body text-center">
                {% if barcode_image %}
                <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode" class="img-fluid mb-2">
                {% endif %}
                <p class="mb-2"><code class="fs-6">{{ product.barcode }}</code></p>
                <button onclick="printBarcode()" class="btn btn-sm btn-primary">
                    <i class="fas fa-print me-1"></i>
                    طباعة الباركود
                </button>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                {% if current_user.can_edit() %}
                <div class="d-grid gap-2">
                    <a href="{{ url_for('invoices.add_invoice_in') }}?product_id={{ product.id }}" class="btn btn-success">
                        <i class="fas fa-arrow-down me-2"></i>
                        إضافة كمية (إدخال)
                    </a>
                    <a href="{{ url_for('invoices.add_invoice_out') }}?product_id={{ product.id }}" class="btn btn-info">
                        <i class="fas fa-arrow-up me-2"></i>
                        إخراج كمية
                    </a>
                    <a href="{{ url_for('products.edit_product', id=product.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج
                    </a>
                </div>
                {% endif %}
                <div class="d-grid gap-2 mt-2">
                    <a href="{{ url_for('reports.product_report', product_id=product.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقرير المنتج
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Product Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-success">{{ product.total_in or 0 }}</div>
                            <small class="text-muted">إجمالي الإدخال</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-danger">{{ product.total_out or 0 }}</div>
                            <small class="text-muted">إجمالي الإخراج</small>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        تم الإنشاء: {{ product.created_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% if product.updated_at %}
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-edit me-1"></i>
                        آخر تحديث: {{ product.updated_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function printBarcode() {
    const barcodeCard = document.querySelector('.card:has(.fa-barcode)');
    if (barcodeCard) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة الباركود - {{ product.name }}</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }
                        .barcode-container { border: 1px solid #ddd; padding: 20px; display: inline-block; }
                        img { max-width: 100%; }
                        .product-name { margin-top: 10px; font-weight: bold; }
                        .barcode-number { margin-top: 5px; font-family: monospace; }
                    </style>
                </head>
                <body>
                    <div class="barcode-container">
                        {% if barcode_image %}
                        <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode">
                        {% endif %}
                        <div class="product-name">{{ product.name }}</div>
                        <div class="barcode-number">{{ product.barcode }}</div>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    </script>
                </body>
            </html>
        `);
        printWindow.document.close();
    }
}
</script>
{% endblock %}
