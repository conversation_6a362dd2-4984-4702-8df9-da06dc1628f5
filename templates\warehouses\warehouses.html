{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-warehouse me-2"></i>
            إدارة المستودعات
        </h1>
        <p class="text-muted">عرض وإدارة جميع المستودعات في النظام</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <a href="{{ url_for('warehouses.add_warehouse') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            إضافة مستودع جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('warehouses.warehouses') }}">
            <div class="row">
                <div class="col-md-10 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="البحث في المستودعات..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Warehouses Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المستودعات</h5>
        <span class="badge bg-primary">{{ warehouses.total }} مستودع</span>
    </div>
    <div class="card-body p-0">
        {% if warehouses.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>اسم المستودع</th>
                        <th>الموقع</th>
                        <th>المسؤول</th>
                        <th>عدد المنتجات</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for warehouse in warehouses.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ warehouse.name }}</strong>
                                {% if warehouse.description %}
                                <br><small class="text-muted">{{ warehouse.description[:50] }}{% if warehouse.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ warehouse.location or '-' }}</td>
                        <td>{{ warehouse.manager or '-' }}</td>
                        <td>
                            <span class="badge bg-info">{{ warehouse.products|length }}</span>
                        </td>
                        <td>
                            <span class="badge bg-{% if warehouse.is_active %}success{% else %}danger{% endif %}">
                                {% if warehouse.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('warehouses.warehouse_detail', id=warehouse.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_edit() %}
                                <a href="{{ url_for('warehouses.edit_warehouse', id=warehouse.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.can_delete() %}
                                <form method="POST" action="{{ url_for('warehouses.delete_warehouse', id=warehouse.id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف المستودع {{ warehouse.name }}؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if warehouses.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Warehouses pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if warehouses.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.warehouses', page=warehouses.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in warehouses.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != warehouses.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('warehouses.warehouses', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if warehouses.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.warehouses', page=warehouses.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مستودعات</h5>
            <p class="text-muted">لم يتم العثور على مستودعات تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('warehouses.add_warehouse') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول مستودع
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_warehouses }}</h4>
                        <p class="mb-0">إجمالي المستودعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-warehouse fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ active_warehouses }}</h4>
                        <p class="mb-0">المستودعات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_products_in_warehouses }}</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ low_stock_products }}</h4>
                        <p class="mb-0">منتجات ناقصة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if current_user.can_edit() %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('warehouses.add_warehouse') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus-circle d-block mb-2" style="font-size: 2rem;"></i>
                            إضافة مستودع
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('products.products') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-boxes d-block mb-2" style="font-size: 2rem;"></i>
                            إدارة المنتجات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('warehouses.categories') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-tags d-block mb-2" style="font-size: 2rem;"></i>
                            إدارة التصنيفات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.warehouse_report') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar d-block mb-2" style="font-size: 2rem;"></i>
                            تقارير المستودعات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
