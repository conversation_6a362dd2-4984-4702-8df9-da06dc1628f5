{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
        </h1>
        <p class="text-muted">مرحباً {{ current_user.full_name }}، إليك نظرة عامة على النظام</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.total_products }}</div>
                    <div class="stats-label">إجمالي المنتجات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.total_warehouses }}</div>
                    <div class="stats-label">المستودعات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.low_stock }}</div>
                    <div class="stats-label">منتجات ناقصة</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card danger-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.total_users }}</div>
                    <div class="stats-label">المستخدمين</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <!-- Recent Products -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    المنتجات المضافة حديثاً
                </h5>
            </div>
            <div class="card-body">
                {% if recent_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الباركود</th>
                                    <th>اسم المنتج</th>
                                    <th>الكمية</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td>{{ product.barcode }}</td>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.quantity }} {{ product.unit_ref.name }}</td>
                                    <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد منتجات مضافة حديثاً</p>
                {% endif %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('products.products') }}" class="btn btn-primary btn-sm">
                        عرض جميع المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Products -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    منتجات تحتاج إعادة تموين
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>المستودع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr class="{% if product.quantity == 0 %}table-danger{% else %}table-warning{% endif %}">
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.quantity }} {{ product.unit_ref.name }}</td>
                                    <td>{{ product.min_quantity }} {{ product.unit_ref.name }}</td>
                                    <td>{{ product.warehouse_ref.name }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-success text-center">
                        <i class="fas fa-check-circle me-2"></i>
                        جميع المنتجات متوفرة بكميات كافية
                    </p>
                {% endif %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('reports.low_stock_report') }}" class="btn btn-warning btn-sm">
                        تقرير مفصل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row">
    <!-- Recent Input Invoices -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-arrow-down me-2"></i>
                    فواتير الإدخال الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_invoices_in %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices_in %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>{{ invoice.supplier.name }}</td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="badge bg-{% if invoice.status == 'completed' %}success{% elif invoice.status == 'pending' %}warning{% else %}danger{% endif %}">
                                            {% if invoice.status == 'completed' %}مكتملة{% elif invoice.status == 'pending' %}معلقة{% else %}ملغية{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد فواتير إدخال حديثة</p>
                {% endif %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('invoices.invoices_in') }}" class="btn btn-success btn-sm">
                        عرض جميع فواتير الإدخال
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Output Invoices -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-arrow-up me-2"></i>
                    فواتير الإخراج الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_invoices_out %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices_out %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>{{ invoice.customer.name }}</td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="badge bg-{% if invoice.status == 'completed' %}success{% elif invoice.status == 'pending' %}warning{% else %}danger{% endif %}">
                                            {% if invoice.status == 'completed' %}مكتملة{% elif invoice.status == 'pending' %}معلقة{% else %}ملغية{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد فواتير إخراج حديثة</p>
                {% endif %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('invoices.invoices_out') }}" class="btn btn-info btn-sm">
                        عرض جميع فواتير الإخراج
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if current_user.can_edit() %}
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('products.add_product') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus-circle d-block mb-2" style="font-size: 2rem;"></i>
                            إضافة منتج
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('invoices.add_invoice_in') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-arrow-down d-block mb-2" style="font-size: 2rem;"></i>
                            فاتورة إدخال
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('invoices.add_invoice_out') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-arrow-up d-block mb-2" style="font-size: 2rem;"></i>
                            فاتورة إخراج
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('reports.inventory_report') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar d-block mb-2" style="font-size: 2rem;"></i>
                            تقرير الجرد
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('products.products') }}" class="btn btn-secondary btn-lg w-100">
                            <i class="fas fa-search d-block mb-2" style="font-size: 2rem;"></i>
                            البحث
                        </a>
                    </div>
                    {% if current_user.role == 'admin' %}
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('settings.backup_database') }}" class="btn btn-dark btn-lg w-100">
                            <i class="fas fa-download d-block mb-2" style="font-size: 2rem;"></i>
                            نسخ احتياطي
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
