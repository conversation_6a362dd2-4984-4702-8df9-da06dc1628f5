{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-tags me-2"></i>
            إدارة التصنيفات
        </h1>
        <p class="text-muted">عرض وإدارة تصنيفات المنتجات</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus me-2"></i>
            إضافة تصنيف جديد
        </button>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('warehouses.categories') }}">
            <div class="row">
                <div class="col-md-10 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="البحث في التصنيفات..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    {% if categories.items %}
    {% for category in categories.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-tag me-2 text-primary"></i>
                    {{ category.name }}
                </h6>
                <span class="badge bg-{% if category.is_active %}success{% else %}danger{% endif %}">
                    {% if category.is_active %}نشط{% else %}غير نشط{% endif %}
                </span>
            </div>
            <div class="card-body">
                {% if category.description %}
                <p class="card-text text-muted">{{ category.description }}</p>
                {% else %}
                <p class="card-text text-muted">لا يوجد وصف</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-primary">{{ category.products|length }}</div>
                            <small class="text-muted">منتج</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-success">{{ category.total_quantity or 0 }}</div>
                            <small class="text-muted">إجمالي الكمية</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('products.products', category_id=category.id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-boxes me-1"></i>
                        المنتجات
                    </a>
                    {% if current_user.can_edit() %}
                    <button type="button" class="btn btn-outline-warning btn-sm" 
                            onclick="editCategory({{ category.id }}, '{{ category.name }}', '{{ category.description or '' }}', {{ category.is_active|lower }})">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </button>
                    {% endif %}
                    {% if current_user.can_delete() and category.products|length == 0 %}
                    <form method="POST" action="{{ url_for('warehouses.delete_category', id=category.id) }}" 
                          style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف التصنيف {{ category.name }}؟')">
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>
                            حذف
                        </button>
                    </form>
                    {% endif %}
                </div>
                
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        {{ category.created_at.strftime('%Y-%m-%d') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    
    {% else %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تصنيفات</h5>
            <p class="text-muted">لم يتم العثور على تصنيفات تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>
                إضافة أول تصنيف
            </button>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if categories.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Categories pagination">
            <ul class="pagination justify-content-center">
                {% if categories.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('warehouses.categories', page=categories.prev_num, **request.args) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in categories.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != categories.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('warehouses.categories', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if categories.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('warehouses.categories', page=categories.next_num, **request.args) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">إضافة تصنيف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('warehouses.add_category') }}">
                {{ add_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ add_form.name.label(class="form-label required") }}
                        {{ add_form.name(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.description.label(class="form-label") }}
                        {{ add_form.description(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ add_form.is_active(class="form-check-input", checked=true) }}
                            {{ add_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ add_form.submit(class="btn btn-success") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">تعديل التصنيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="editCategoryForm">
                {{ edit_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ edit_form.name.label(class="form-label required") }}
                        {{ edit_form.name(class="form-control", id="edit_name") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.description.label(class="form-label") }}
                        {{ edit_form.description(class="form-control", id="edit_description") }}
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ edit_form.is_active(class="form-check-input", id="edit_is_active") }}
                            {{ edit_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ edit_form.submit(class="btn btn-warning") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editCategory(id, name, description, isActive) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_description').value = description;
    document.getElementById('edit_is_active').checked = isActive;
    
    const form = document.getElementById('editCategoryForm');
    form.action = `/warehouses/categories/${id}/edit`;
    
    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
    modal.show();
}

// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}
