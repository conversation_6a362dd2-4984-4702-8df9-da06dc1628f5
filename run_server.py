#!/usr/bin/env python3
"""
تشغيل خادم نظام إدارة المستودعات
"""

import os
import sys
from app import create_app

def main():
    """تشغيل التطبيق"""
    try:
        # إنشاء التطبيق
        app = create_app()
        
        # تشغيل الخادم
        print("بدء تشغيل نظام إدارة المستودعات...")
        print("يمكنك الوصول للنظام عبر: http://127.0.0.1:5000")
        print("للإيقاف اضغط Ctrl+C")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
