from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={"placeholder": "أدخل اسم المستخدم", "class": "form-control"})
    password = PasswordField('كلمة المرور', validators=[DataRequired()],
                           render_kw={"placeholder": "أدخل كلمة المرور", "class": "form-control"})
    remember_me = BooleanField('تذكرني', render_kw={"class": "form-check-input"})
    submit = SubmitField('تسجيل الدخول', render_kw={"class": "btn btn-primary w-100"})

class UserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)],
                          render_kw={"placeholder": "أدخل اسم المستخدم", "class": "form-control"})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={"placeholder": "أدخل البريد الإلكتروني", "class": "form-control"})
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={"placeholder": "أدخل الاسم الكامل", "class": "form-control"})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={"placeholder": "أدخل كلمة المرور", "class": "form-control"})
    password2 = PasswordField('تأكيد كلمة المرور', 
                            validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')],
                            render_kw={"placeholder": "أعد إدخال كلمة المرور", "class": "form-control"})
    role = SelectField('الصلاحية', 
                      choices=[('admin', 'مدير'), ('employee', 'موظف'), ('viewer', 'مشاهد')],
                      validators=[DataRequired()],
                      render_kw={"class": "form-select"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})
    
    def __init__(self, original_user=None, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.original_user = original_user
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None and (self.original_user is None or user.id != self.original_user.id):
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None and (self.original_user is None or user.id != self.original_user.id):
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى اختيار بريد آخر.')

class EditUserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)],
                          render_kw={"placeholder": "أدخل اسم المستخدم", "class": "form-control"})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={"placeholder": "أدخل البريد الإلكتروني", "class": "form-control"})
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={"placeholder": "أدخل الاسم الكامل", "class": "form-control"})
    role = SelectField('الصلاحية', 
                      choices=[('admin', 'مدير'), ('employee', 'موظف'), ('viewer', 'مشاهد')],
                      validators=[DataRequired()],
                      render_kw={"class": "form-select"})
    submit = SubmitField('تحديث', render_kw={"class": "btn btn-success"})
    
    def __init__(self, original_user, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.original_user = original_user
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None and user.id != self.original_user.id:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None and user.id != self.original_user.id:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى اختيار بريد آخر.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()],
                                   render_kw={"placeholder": "أدخل كلمة المرور الحالية", "class": "form-control"})
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)],
                               render_kw={"placeholder": "أدخل كلمة المرور الجديدة", "class": "form-control"})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة', 
                                validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')],
                                render_kw={"placeholder": "أعد إدخال كلمة المرور الجديدة", "class": "form-control"})
    submit = SubmitField('تغيير كلمة المرور', render_kw={"class": "btn btn-primary"})
