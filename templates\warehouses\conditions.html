{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-check-circle me-2"></i>
            إدارة الحالات
        </h1>
        <p class="text-muted">عرض وإدارة حالات المنتجات (جديد، مستعمل، تالف، إلخ)</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addConditionModal">
            <i class="fas fa-plus me-2"></i>
            إضافة حالة جديدة
        </button>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('warehouses.conditions') }}">
            <div class="row">
                <div class="col-md-10 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="البحث في الحالات..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Conditions Grid -->
<div class="row">
    {% if conditions.items %}
    {% for condition in conditions.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-circle me-2" style="color: {{ condition.color or '#6c757d' }};"></i>
                    {{ condition.name }}
                </h6>
                <span class="badge bg-{% if condition.is_active %}success{% else %}danger{% endif %}">
                    {% if condition.is_active %}نشط{% else %}غير نشط{% endif %}
                </span>
            </div>
            <div class="card-body">
                {% if condition.description %}
                <p class="card-text text-muted">{{ condition.description }}</p>
                {% else %}
                <p class="card-text text-muted">لا يوجد وصف</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-primary">{{ condition.products|length }}</div>
                            <small class="text-muted">منتج</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <div class="h5 mb-1 text-success">{{ condition.total_quantity or 0 }}</div>
                            <small class="text-muted">إجمالي الكمية</small>
                        </div>
                    </div>
                </div>
                
                {% if condition.color %}
                <div class="mt-3">
                    <small class="text-muted">اللون:</small>
                    <span class="badge" style="background-color: {{ condition.color }}; color: white;">{{ condition.color }}</span>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('products.products', condition_id=condition.id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-boxes me-1"></i>
                        المنتجات
                    </a>
                    {% if current_user.can_edit() %}
                    <button type="button" class="btn btn-outline-warning btn-sm" 
                            onclick="editCondition({{ condition.id }}, '{{ condition.name }}', '{{ condition.description or '' }}', '{{ condition.color or '' }}', {{ condition.is_active|lower }})">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </button>
                    {% endif %}
                    {% if current_user.can_delete() and condition.products|length == 0 %}
                    <form method="POST" action="{{ url_for('warehouses.delete_condition', id=condition.id) }}" 
                          style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف الحالة {{ condition.name }}؟')">
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>
                            حذف
                        </button>
                    </form>
                    {% endif %}
                </div>
                
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        {{ condition.created_at.strftime('%Y-%m-%d') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    
    {% else %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد حالات</h5>
            <p class="text-muted">لم يتم العثور على حالات تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConditionModal">
                <i class="fas fa-plus me-2"></i>
                إضافة أول حالة
            </button>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if conditions.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Conditions pagination">
            <ul class="pagination justify-content-center">
                {% if conditions.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('warehouses.conditions', page=conditions.prev_num, **request.args) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in conditions.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != conditions.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('warehouses.conditions', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if conditions.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('warehouses.conditions', page=conditions.next_num, **request.args) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

<!-- Add Condition Modal -->
<div class="modal fade" id="addConditionModal" tabindex="-1" aria-labelledby="addConditionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addConditionModalLabel">إضافة حالة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('warehouses.add_condition') }}">
                {{ add_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ add_form.name.label(class="form-label required") }}
                        {{ add_form.name(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.description.label(class="form-label") }}
                        {{ add_form.description(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_form.color.label(class="form-label") }}
                        {{ add_form.color(class="form-control") }}
                        <div class="form-text">اختر لوناً لتمييز هذه الحالة</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ add_form.is_active(class="form-check-input", checked=true) }}
                            {{ add_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ add_form.submit(class="btn btn-success") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Condition Modal -->
<div class="modal fade" id="editConditionModal" tabindex="-1" aria-labelledby="editConditionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editConditionModalLabel">تعديل الحالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="editConditionForm">
                {{ edit_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ edit_form.name.label(class="form-label required") }}
                        {{ edit_form.name(class="form-control", id="edit_name") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.description.label(class="form-label") }}
                        {{ edit_form.description(class="form-control", id="edit_description") }}
                    </div>
                    <div class="mb-3">
                        {{ edit_form.color.label(class="form-label") }}
                        {{ edit_form.color(class="form-control", id="edit_color") }}
                        <div class="form-text">اختر لوناً لتمييز هذه الحالة</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            {{ edit_form.is_active(class="form-check-input", id="edit_is_active") }}
                            {{ edit_form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ edit_form.submit(class="btn btn-warning") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editCondition(id, name, description, color, isActive) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_description').value = description;
    document.getElementById('edit_color').value = color;
    document.getElementById('edit_is_active').checked = isActive;
    
    const form = document.getElementById('editConditionForm');
    form.action = `/warehouses/conditions/${id}/edit`;
    
    const modal = new bootstrap.Modal(document.getElementById('editConditionModal'));
    modal.show();
}

// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}
