{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-users me-2"></i>
            إدارة العملاء
        </h1>
        <p class="text-muted">عرض وإدارة بيانات العملاء</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <a href="{{ url_for('warehouses.add_customer') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            إضافة عميل جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('warehouses.customers') }}">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني)..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <select name="customer_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="individual" {% if request.args.get('customer_type') == 'individual' %}selected{% endif %}>فرد</option>
                        <option value="company" {% if request.args.get('customer_type') == 'company' %}selected{% endif %}>شركة</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة العملاء</h5>
        <span class="badge bg-primary">{{ customers.total }} عميل</span>
    </div>
    <div class="card-body p-0">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>اسم العميل</th>
                        <th>النوع</th>
                        <th>معلومات الاتصال</th>
                        <th>العنوان</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ customer.name }}</strong>
                                {% if customer.company_name %}
                                <br><small class="text-muted">{{ customer.company_name }}</small>
                                {% endif %}
                                {% if customer.tax_number %}
                                <br><small class="text-info">ضريبي: {{ customer.tax_number }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{% if customer.customer_type == 'company' %}info{% else %}secondary{% endif %}">
                                {% if customer.customer_type == 'company' %}شركة{% else %}فرد{% endif %}
                            </span>
                        </td>
                        <td>
                            {% if customer.phone %}
                            <div><i class="fas fa-phone me-1"></i>{{ customer.phone }}</div>
                            {% endif %}
                            {% if customer.email %}
                            <div><i class="fas fa-envelope me-1"></i>{{ customer.email }}</div>
                            {% endif %}
                            {% if not customer.phone and not customer.email %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.address %}
                            {{ customer.address[:30] }}{% if customer.address|length > 30 %}...{% endif %}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{% if customer.is_active %}success{% else %}danger{% endif %}">
                                {% if customer.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('warehouses.customer_detail', id=customer.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_edit() %}
                                <a href="{{ url_for('warehouses.edit_customer', id=customer.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.can_delete() %}
                                <form method="POST" action="{{ url_for('warehouses.delete_customer', id=customer.id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف العميل {{ customer.name }}؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Customers pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if customers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.customers', page=customers.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in customers.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != customers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('warehouses.customers', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if customers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('warehouses.customers', page=customers.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">لم يتم العثور على عملاء يطابقون معايير البحث</p>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('warehouses.add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول عميل
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_customers }}</h4>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ active_customers }}</h4>
                        <p class="mb-0">العملاء النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ company_customers }}</h4>
                        <p class="mb-0">عملاء الشركات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ individual_customers }}</h4>
                        <p class="mb-0">العملاء الأفراد</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if current_user.can_edit() %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('warehouses.add_customer') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-user-plus d-block mb-2" style="font-size: 2rem;"></i>
                            إضافة عميل
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('invoices.invoices_out') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-file-invoice d-block mb-2" style="font-size: 2rem;"></i>
                            فواتير البيع
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('warehouses.suppliers') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-truck d-block mb-2" style="font-size: 2rem;"></i>
                            إدارة الموردين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.customers_report') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar d-block mb-2" style="font-size: 2rem;"></i>
                            تقارير العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const customerTypeSelect = document.querySelector('select[name="customer_type"]');
    
    if (searchInput) {
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
    
    if (customerTypeSelect) {
        customerTypeSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
{% endblock %}
