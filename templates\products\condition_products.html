{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-boxes me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted">عرض جميع المنتجات في حالة {{ condition.name }}</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('products.products') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمنتجات
        </a>
        {% if current_user.can_edit() %}
        <a href="{{ url_for('products.add_product') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث في منتجات حالة {{ condition.name }}
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('products.condition_products', id=condition.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="ابحث بالاسم أو الباركود..." value="{{ search or '' }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <a href="{{ url_for('products.condition_products', id=condition.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المنتجات ({{ products.total }} منتج)
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="printPage()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel('products-table', 'condition_products')">
                <i class="fas fa-file-excel me-1"></i>
                تصدير Excel
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="products-table">
                <thead class="table-dark">
                    <tr>
                        <th>الباركود</th>
                        <th>اسم المنتج</th>
                        <th>التصنيف</th>
                        <th>وحدة القياس</th>
                        <th>الكمية</th>
                        <th>الحد الأدنى</th>
                        <th>نوع المنتج</th>
                        <th>المستودع</th>
                        {% if current_user.can_edit() or current_user.can_delete() %}
                        <th>الإجراءات</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr>
                        <td>
                            <code>{{ product.barcode }}</code>
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                            <br><small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                            {% endif %}
                        </td>
                        <td>{{ product.category_ref.name if product.category_ref else '-' }}</td>
                        <td>{{ product.unit_ref.name if product.unit_ref else '-' }}</td>
                        <td>
                            <span class="badge {% if product.quantity <= product.min_quantity %}bg-danger{% else %}bg-success{% endif %}">
                                {{ product.quantity }}
                            </span>
                        </td>
                        <td>{{ product.min_quantity }}</td>
                        <td>
                            <span class="badge {% if product.product_type == 'raw' %}bg-info{% else %}bg-primary{% endif %}">
                                {% if product.product_type == 'raw' %}مادة خام{% else %}منتج نهائي{% endif %}
                            </span>
                        </td>
                        <td>{{ product.warehouse_ref.name if product.warehouse_ref else '-' }}</td>
                        {% if current_user.can_edit() or current_user.can_delete() %}
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('products.product_detail', id=product.id) }}" 
                                   class="btn btn-outline-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_edit() %}
                                <a href="{{ url_for('products.edit_product', id=product.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.can_delete() %}
                                <a href="{{ url_for('products.delete_product', id=product.id) }}" 
                                   class="btn btn-outline-danger" title="حذف"
                                   onclick="return confirmDelete('هل أنت متأكد من حذف هذا المنتج؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="صفحات المنتجات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products.condition_products', id=condition.id, page=products.prev_num, search=search) }}">
                        السابق
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products.condition_products', id=condition.id, page=page_num, search=search) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products.condition_products', id=condition.id, page=products.next_num, search=search) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات في هذه الحالة</h5>
            <p class="text-muted">{% if search %}لم يتم العثور على منتجات تطابق البحث "{{ search }}"{% else %}لا توجد منتجات مضافة في حالة {{ condition.name }} حتى الآن{% endif %}</p>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('products.add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
