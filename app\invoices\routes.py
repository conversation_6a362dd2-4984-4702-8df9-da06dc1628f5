from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import datetime, timezone
from app import db
from app.invoices import bp
from app.models import (InvoiceIn, InvoiceOut, InvoiceInItem, InvoiceOutItem,
                       ProductionInvoice, ProductionInvoiceItem, PurchaseOrder,
                       Product, Supplier, Customer, Warehouse)


@bp.route('/invoices_in')
@login_required
def invoices_in():
    """عرض قائمة فواتير الشراء"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    supplier_id = request.args.get('supplier_id')
    if supplier_id and supplier_id.isdigit():
        supplier_id = int(supplier_id)
    else:
        supplier_id = None
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')
    
    query = InvoiceIn.query
    
    # Apply filters
    if search:
        query = query.filter(
            db.or_(
                InvoiceIn.invoice_number.contains(search),
                InvoiceIn.reference_number.contains(search)
            )
        )
    
    if supplier_id:
        query = query.filter(InvoiceIn.supplier_id == supplier_id)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    if status:
        query = query.filter(InvoiceIn.status == status)
    
    # Order by date descending
    query = query.order_by(InvoiceIn.invoice_date.desc())
    
    invoices = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get suppliers for filter dropdown
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
    
    # Calculate statistics
    total_invoices = InvoiceIn.query.count()
    total_amount = db.session.query(db.func.sum(InvoiceIn.total_amount)).scalar() or 0
    pending_invoices = InvoiceIn.query.filter(InvoiceIn.status.in_(['draft', 'confirmed'])).count()
    received_invoices = InvoiceIn.query.filter_by(status='received').count()
    
    return render_template('invoices/invoices_in.html',
                         invoices=invoices,
                         suppliers=suppliers,
                         total_invoices=total_invoices,
                         total_amount=total_amount,
                         pending_invoices=pending_invoices,
                         received_invoices=received_invoices)


@bp.route('/invoices_out')
@login_required
def invoices_out():
    """عرض قائمة فواتير البيع"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    customer_id = request.args.get('customer_id')
    if customer_id and customer_id.isdigit():
        customer_id = int(customer_id)
    else:
        customer_id = None
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')
    
    query = InvoiceOut.query
    
    # Apply filters
    if search:
        query = query.filter(
            db.or_(
                InvoiceOut.invoice_number.contains(search),
                InvoiceOut.reference_number.contains(search)
            )
        )
    
    if customer_id:
        query = query.filter(InvoiceOut.customer_id == customer_id)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    if status:
        query = query.filter(InvoiceOut.status == status)
    
    # Order by date descending
    query = query.order_by(InvoiceOut.invoice_date.desc())
    
    invoices = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get customers for filter dropdown
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    
    # Calculate statistics
    total_invoices = InvoiceOut.query.count()
    total_amount = db.session.query(db.func.sum(InvoiceOut.total_amount)).scalar() or 0
    pending_invoices = InvoiceOut.query.filter(InvoiceOut.status.in_(['draft', 'confirmed'])).count()
    delivered_invoices = InvoiceOut.query.filter_by(status='delivered').count()
    
    return render_template('invoices/invoices_out.html',
                         invoices=invoices,
                         customers=customers,
                         total_invoices=total_invoices,
                         total_amount=total_amount,
                         pending_invoices=pending_invoices,
                         delivered_invoices=delivered_invoices)


@bp.route('/add_invoice_in')
@login_required
def add_invoice_in():
    """إضافة فاتورة شراء جديدة"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لإضافة الفواتير', 'error')
        return redirect(url_for('invoices.invoices_in'))
    
    # Get data for form dropdowns
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    
    return render_template('invoices/invoice_in_form.html',
                         action='add',
                         suppliers=suppliers,
                         warehouses=warehouses,
                         products=products)


@bp.route('/add_invoice_out')
@login_required
def add_invoice_out():
    """إضافة فاتورة بيع جديدة"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لإضافة الفواتير', 'error')
        return redirect(url_for('invoices.invoices_out'))
    
    # Get data for form dropdowns
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    
    return render_template('invoices/invoice_out_form.html',
                         action='add',
                         customers=customers,
                         warehouses=warehouses,
                         products=products)


@bp.route('/invoice_in/<int:id>')
@login_required
def invoice_in_detail(id):
    """عرض تفاصيل فاتورة الشراء"""
    invoice = InvoiceIn.query.get_or_404(id)
    return render_template('invoices/invoice_in_detail.html', invoice=invoice)


@bp.route('/invoice_out/<int:id>')
@login_required
def invoice_out_detail(id):
    """عرض تفاصيل فاتورة البيع"""
    invoice = InvoiceOut.query.get_or_404(id)
    return render_template('invoices/invoice_out_detail.html', invoice=invoice)


@bp.route('/edit_invoice_in/<int:id>')
@login_required
def edit_invoice_in(id):
    """تعديل فاتورة الشراء"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لتعديل الفواتير', 'error')
        return redirect(url_for('invoices.invoices_in'))
    
    invoice = InvoiceIn.query.get_or_404(id)
    
    if invoice.status == 'received':
        flash('لا يمكن تعديل فاتورة مستلمة', 'error')
        return redirect(url_for('invoices.invoice_in_detail', id=id))
    
    # Get data for form dropdowns
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    
    return render_template('invoices/invoice_in_form.html',
                         action='edit',
                         invoice=invoice,
                         suppliers=suppliers,
                         warehouses=warehouses,
                         products=products)


@bp.route('/edit_invoice_out/<int:id>')
@login_required
def edit_invoice_out(id):
    """تعديل فاتورة البيع"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لتعديل الفواتير', 'error')
        return redirect(url_for('invoices.invoices_out'))
    
    invoice = InvoiceOut.query.get_or_404(id)
    
    if invoice.status == 'delivered':
        flash('لا يمكن تعديل فاتورة مسلمة', 'error')
        return redirect(url_for('invoices.invoice_out_detail', id=id))
    
    # Get data for form dropdowns
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    
    return render_template('invoices/invoice_out_form.html',
                         action='edit',
                         invoice=invoice,
                         customers=customers,
                         warehouses=warehouses,
                         products=products)


@bp.route('/print_invoice_in/<int:id>')
@login_required
def print_invoice_in(id):
    """طباعة فاتورة الشراء"""
    invoice = InvoiceIn.query.get_or_404(id)
    return render_template('invoices/print_invoice_in.html', invoice=invoice)


@bp.route('/print_invoice_out/<int:id>')
@login_required
def print_invoice_out(id):
    """طباعة فاتورة البيع"""
    invoice = InvoiceOut.query.get_or_404(id)
    return render_template('invoices/print_invoice_out.html', invoice=invoice)


@bp.route('/receive_invoice_in/<int:id>', methods=['POST'])
@login_required
def receive_invoice_in(id):
    """استلام فاتورة الشراء وتحديث المخزون"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لاستلام الفواتير', 'error')
        return redirect(url_for('invoices.invoices_in'))
    
    invoice = InvoiceIn.query.get_or_404(id)
    
    if invoice.status != 'confirmed':
        flash('يمكن استلام الفواتير المؤكدة فقط', 'error')
        return redirect(url_for('invoices.invoice_in_detail', id=id))
    
    try:
        # Update invoice status
        invoice.status = 'received'
        invoice.received_at = datetime.now(timezone.utc)
        invoice.received_by = current_user.id
        
        # Update product stock for each item
        for item in invoice.items:
            product = item.product_ref
            # Add quantity to stock
            if hasattr(product, 'stock_quantity'):
                product.stock_quantity = (product.stock_quantity or 0) + item.quantity
            
        db.session.commit()
        flash(f'تم استلام الفاتورة {invoice.invoice_number} بنجاح وتحديث المخزون', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء استلام الفاتورة', 'error')
    
    return redirect(url_for('invoices.invoice_in_detail', id=id))


@bp.route('/deliver_invoice_out/<int:id>', methods=['POST'])
@login_required
def deliver_invoice_out(id):
    """تسليم فاتورة البيع وتحديث المخزون"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لتسليم الفواتير', 'error')
        return redirect(url_for('invoices.invoices_out'))
    
    invoice = InvoiceOut.query.get_or_404(id)
    
    if invoice.status != 'confirmed':
        flash('يمكن تسليم الفواتير المؤكدة فقط', 'error')
        return redirect(url_for('invoices.invoice_out_detail', id=id))
    
    try:
        # Check stock availability
        for item in invoice.items:
            product = item.product_ref
            current_stock = getattr(product, 'stock_quantity', 0) or 0
            if current_stock < item.quantity:
                flash(f'المخزون غير كافي للمنتج {product.name}. المتوفر: {current_stock}، المطلوب: {item.quantity}', 'error')
                return redirect(url_for('invoices.invoice_out_detail', id=id))
        
        # Update invoice status
        invoice.status = 'delivered'
        invoice.delivered_at = datetime.now(timezone.utc)
        invoice.delivered_by = current_user.id
        
        # Update product stock for each item
        for item in invoice.items:
            product = item.product_ref
            # Subtract quantity from stock
            if hasattr(product, 'stock_quantity'):
                product.stock_quantity = (product.stock_quantity or 0) - item.quantity
        
        db.session.commit()
        flash(f'تم تسليم الفاتورة {invoice.invoice_number} بنجاح وتحديث المخزون', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تسليم الفاتورة', 'error')
    
    return redirect(url_for('invoices.invoice_out_detail', id=id))


@bp.route('/delete_invoice_in/<int:id>', methods=['POST'])
@login_required
def delete_invoice_in(id):
    """حذف فاتورة الشراء"""
    if not current_user.can_delete():
        flash('ليس لديك صلاحية لحذف الفواتير', 'error')
        return redirect(url_for('invoices.invoices_in'))
    
    invoice = InvoiceIn.query.get_or_404(id)
    
    if invoice.status != 'draft':
        flash('يمكن حذف المسودات فقط', 'error')
        return redirect(url_for('invoices.invoice_in_detail', id=id))
    
    try:
        # Delete invoice items first
        for item in invoice.items:
            db.session.delete(item)
        
        # Delete invoice
        db.session.delete(invoice)
        db.session.commit()
        flash(f'تم حذف الفاتورة {invoice.invoice_number} بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الفاتورة', 'error')
    
    return redirect(url_for('invoices.invoices_in'))


@bp.route('/delete_invoice_out/<int:id>', methods=['POST'])
@login_required
def delete_invoice_out(id):
    """حذف فاتورة البيع"""
    if not current_user.can_delete():
        flash('ليس لديك صلاحية لحذف الفواتير', 'error')
        return redirect(url_for('invoices.invoices_out'))
    
    invoice = InvoiceOut.query.get_or_404(id)
    
    if invoice.status != 'draft':
        flash('يمكن حذف المسودات فقط', 'error')
        return redirect(url_for('invoices.invoice_out_detail', id=id))
    
    try:
        # Delete invoice items first
        for item in invoice.items:
            db.session.delete(item)
        
        # Delete invoice
        db.session.delete(invoice)
        db.session.commit()
        flash(f'تم حذف الفاتورة {invoice.invoice_number} بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الفاتورة', 'error')
    
    return redirect(url_for('invoices.invoices_out'))


@bp.route('/production_invoices')
@login_required
def production_invoices():
    """عرض قائمة فواتير الإنتاج"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')

    query = ProductionInvoice.query

    # Apply filters
    if search:
        query = query.filter(
            db.or_(
                ProductionInvoice.invoice_number.contains(search),
                ProductionInvoice.finished_product.has(Product.name.contains(search))
            )
        )

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(ProductionInvoice.production_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(ProductionInvoice.production_date <= date_to_obj)
        except ValueError:
            pass

    if status:
        query = query.filter(ProductionInvoice.status == status)

    # Order by date descending
    query = query.order_by(ProductionInvoice.production_date.desc())

    invoices = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # Calculate statistics
    total_invoices = ProductionInvoice.query.count()
    pending_invoices = ProductionInvoice.query.filter(ProductionInvoice.status.in_(['draft', 'confirmed'])).count()
    completed_invoices = ProductionInvoice.query.filter_by(status='completed').count()

    return render_template('invoices/production_invoices.html',
                         invoices=invoices,
                         total_invoices=total_invoices,
                         pending_invoices=pending_invoices,
                         completed_invoices=completed_invoices)


@bp.route('/purchase_orders')
@login_required
def purchase_orders():
    """عرض قائمة طلبات الشراء"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    supplier_id = request.args.get('supplier_id')
    if supplier_id and supplier_id.isdigit():
        supplier_id = int(supplier_id)
    else:
        supplier_id = None
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')

    query = PurchaseOrder.query

    # Apply filters
    if search:
        query = query.filter(
            db.or_(
                PurchaseOrder.order_number.contains(search),
                PurchaseOrder.reference_number.contains(search)
            )
        )

    if supplier_id:
        query = query.filter(PurchaseOrder.supplier_id == supplier_id)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(PurchaseOrder.order_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(PurchaseOrder.order_date <= date_to_obj)
        except ValueError:
            pass

    if status:
        query = query.filter(PurchaseOrder.status == status)

    # Order by date descending
    query = query.order_by(PurchaseOrder.order_date.desc())

    orders = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # Get suppliers for filter dropdown
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()

    # Calculate statistics
    total_orders = PurchaseOrder.query.count()
    total_amount = db.session.query(db.func.sum(PurchaseOrder.total_amount)).scalar() or 0
    pending_orders = PurchaseOrder.query.filter(PurchaseOrder.status.in_(['draft', 'sent'])).count()
    received_orders = PurchaseOrder.query.filter_by(status='received').count()

    return render_template('invoices/purchase_orders.html',
                         orders=orders,
                         suppliers=suppliers,
                         total_orders=total_orders,
                         total_amount=total_amount,
                         pending_orders=pending_orders,
                         received_orders=received_orders)


@bp.route('/add_production_invoice')
@login_required
def add_production_invoice():
    """إضافة فاتورة إنتاج جديدة"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لإضافة فواتير الإنتاج', 'error')
        return redirect(url_for('invoices.production_invoices'))

    # Get products for dropdowns
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()

    return render_template('invoices/production_invoice_form.html',
                         action='add',
                         products=products,
                         warehouses=warehouses)


@bp.route('/add_purchase_order')
@login_required
def add_purchase_order():
    """إضافة طلب شراء جديد"""
    if not current_user.can_edit():
        flash('ليس لديك صلاحية لإضافة طلبات الشراء', 'error')
        return redirect(url_for('invoices.purchase_orders'))

    # Get data for form dropdowns
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()

    return render_template('invoices/purchase_order_form.html',
                         action='add',
                         suppliers=suppliers,
                         warehouses=warehouses,
                         products=products)
