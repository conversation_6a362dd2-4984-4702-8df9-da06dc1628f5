{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-import me-2"></i>
            فواتير الإدخال (الشراء)
        </h1>
        <p class="text-muted">عرض وإدارة فواتير شراء البضائع من الموردين</p>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.can_edit() %}
        <a href="{{ url_for('invoices.add_invoice_in') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            إضافة فاتورة شراء
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('invoices.invoices_in') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <input type="text" name="search" class="form-control" placeholder="رقم الفاتورة أو المورد..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <select name="supplier_id" class="form-select">
                        <option value="">جميع الموردين</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if request.args.get('supplier_id')|int == supplier.id %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <input type="date" name="date_from" class="form-control" 
                           value="{{ request.args.get('date_from', '') }}" placeholder="من تاريخ">
                </div>
                <div class="col-md-2 mb-3">
                    <input type="date" name="date_to" class="form-control" 
                           value="{{ request.args.get('date_to', '') }}" placeholder="إلى تاريخ">
                </div>
                <div class="col-md-2 mb-3">
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {% if request.args.get('status') == 'draft' %}selected{% endif %}>مسودة</option>
                        <option value="confirmed" {% if request.args.get('status') == 'confirmed' %}selected{% endif %}>مؤكدة</option>
                        <option value="received" {% if request.args.get('status') == 'received' %}selected{% endif %}>مستلمة</option>
                    </select>
                </div>
                <div class="col-md-1 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة فواتير الشراء</h5>
        <span class="badge bg-primary">{{ invoices.total }} فاتورة</span>
    </div>
    <div class="card-body p-0">
        {% if invoices.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المورد</th>
                        <th>التاريخ</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices.items %}
                    <tr>
                        <td>
                            <strong>{{ invoice.invoice_number }}</strong>
                            {% if invoice.reference_number %}
                            <br><small class="text-muted">مرجع: {{ invoice.reference_number }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                {{ invoice.supplier_ref.name }}
                                {% if invoice.supplier_ref.company_name %}
                                <br><small class="text-muted">{{ invoice.supplier_ref.company_name }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <strong class="text-success">{{ format_currency(invoice.total_amount) }}</strong>
                            {% if invoice.items|length > 0 %}
                            <br><small class="text-muted">{{ invoice.items|length }} صنف</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{% if invoice.status == 'received' %}success{% elif invoice.status == 'confirmed' %}warning{% else %}secondary{% endif %}">
                                {% if invoice.status == 'draft' %}مسودة
                                {% elif invoice.status == 'confirmed' %}مؤكدة
                                {% elif invoice.status == 'received' %}مستلمة
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('invoices.invoice_in_detail', id=invoice.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_edit() and invoice.status != 'received' %}
                                <a href="{{ url_for('invoices.edit_invoice_in', id=invoice.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('invoices.print_invoice_in', id=invoice.id) }}" 
                                   class="btn btn-outline-info" title="طباعة" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                {% if current_user.can_edit() and invoice.status == 'confirmed' %}
                                <form method="POST" action="{{ url_for('invoices.receive_invoice_in', id=invoice.id) }}" 
                                      style="display: inline;" onsubmit="return confirm('هل أنت متأكد من استلام هذه الفاتورة؟ سيتم تحديث المخزون.')">
                                    <button type="submit" class="btn btn-outline-success" title="استلام">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                {% endif %}
                                {% if current_user.can_delete() and invoice.status == 'draft' %}
                                <form method="POST" action="{{ url_for('invoices.delete_invoice_in', id=invoice.id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف الفاتورة {{ invoice.invoice_number }}؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if invoices.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Invoices pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if invoices.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('invoices.invoices_in', page=invoices.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in invoices.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != invoices.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('invoices.invoices_in', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if invoices.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('invoices.invoices_in', page=invoices.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-import fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير شراء</h5>
            <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
            {% if current_user.can_edit() %}
            <a href="{{ url_for('invoices.add_invoice_in') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول فاتورة شراء
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_invoices }}</h4>
                        <p class="mb-0">إجمالي الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-import fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ format_currency(total_amount) }}</h4>
                        <p class="mb-0">إجمالي المبلغ</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ pending_invoices }}</h4>
                        <p class="mb-0">فواتير معلقة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ received_invoices }}</h4>
                        <p class="mb-0">فواتير مستلمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    const statusSelect = document.querySelector('select[name="status"]');
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');
    
    if (searchInput) {
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
    
    [supplierSelect, statusSelect, dateFromInput, dateToInput].forEach(element => {
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script>
{% endblock %}
