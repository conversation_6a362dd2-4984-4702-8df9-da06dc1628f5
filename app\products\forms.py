from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, SelectField, FloatField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, NumberRange, ValidationError
from app.models import Product, Category, Unit, Condition, Warehouse

def safe_int_coerce(value):
    """Safely coerce value to int, return None for empty strings"""
    if value == '' or value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None

class ProductForm(FlaskForm):
    barcode = StringField('الباركود', validators=[DataRequired(), Length(min=10, max=50)],
                         render_kw={"placeholder": "أدخل الباركود أو اضغط 'توليد'", "class": "form-control"})
    name = StringField('اسم المنتج', validators=[DataRequired(), Length(min=2, max=200)],
                      render_kw={"placeholder": "أدخل اسم المنتج", "class": "form-control"})
    description = TextAreaField('الوصف',
                               render_kw={"placeholder": "أدخل وصف المنتج", "class": "form-control", "rows": "3"})
    category_id = SelectField('التصنيف', coerce=int, validators=[DataRequired()],
                             render_kw={"class": "form-select"})
    unit_id = SelectField('وحدة القياس', coerce=int, validators=[DataRequired()],
                         render_kw={"class": "form-select"})
    condition_id = SelectField('حالة المادة', coerce=int, validators=[DataRequired()],
                              render_kw={"class": "form-select"})
    warehouse_id = SelectField('المستودع', coerce=int, validators=[DataRequired()],
                              render_kw={"class": "form-select"})
    quantity = FloatField('الكمية', validators=[DataRequired(), NumberRange(min=0)],
                         render_kw={"placeholder": "أدخل الكمية", "class": "form-control", "step": "0.01"})
    min_quantity = FloatField('الحد الأدنى للكمية', validators=[NumberRange(min=0)], default=0,
                             render_kw={"placeholder": "أدخل الحد الأدنى", "class": "form-control", "step": "0.01"})
    product_type = SelectField('نوع المنتج', 
                              choices=[('raw', 'مادة خام'), ('finished', 'منتج نهائي')],
                              validators=[DataRequired()],
                              render_kw={"class": "form-select"})
    image = FileField('صورة المنتج', validators=[FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'الصور فقط!')],
                     render_kw={"class": "form-control"})
    notes = TextAreaField('ملاحظات',
                         render_kw={"placeholder": "أدخل ملاحظات إضافية", "class": "form-control", "rows": "3"})
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-success"})
    
    def __init__(self, original_product=None, *args, **kwargs):
        super(ProductForm, self).__init__(*args, **kwargs)
        self.original_product = original_product
        
        # Populate choices
        self.category_id.choices = [(c.id, c.name) for c in Category.query.filter_by(is_active=True).order_by(Category.name).all()]
        self.unit_id.choices = [(u.id, u.name) for u in Unit.query.filter_by(is_active=True).order_by(Unit.name).all()]
        self.condition_id.choices = [(c.id, c.name) for c in Condition.query.filter_by(is_active=True).order_by(Condition.name).all()]
        self.warehouse_id.choices = [(w.id, w.name) for w in Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()]
    
    def validate_barcode(self, barcode):
        product = Product.query.filter_by(barcode=barcode.data).first()
        if product is not None and (self.original_product is None or product.id != self.original_product.id):
            raise ValidationError('الباركود مستخدم بالفعل. يرجى اختيار باركود آخر.')

class ProductSearchForm(FlaskForm):
    search = StringField('البحث', 
                        render_kw={"placeholder": "ابحث بالاسم أو الباركود...", "class": "form-control"})
    category_id = SelectField('التصنيف', coerce=safe_int_coerce,
                             render_kw={"class": "form-select"})
    warehouse_id = SelectField('المستودع', coerce=safe_int_coerce,
                              render_kw={"class": "form-select"})
    product_type = SelectField('نوع المنتج',
                              choices=[('', 'جميع الأنواع'), ('raw', 'مادة خام'), ('finished', 'منتج نهائي')],
                              render_kw={"class": "form-select"})
    low_stock = BooleanField('المنتجات الناقصة فقط', render_kw={"class": "form-check-input"})
    submit = SubmitField('بحث', render_kw={"class": "btn btn-primary"})
    
    def __init__(self, *args, **kwargs):
        super(ProductSearchForm, self).__init__(*args, **kwargs)
        
        # Populate choices
        self.category_id.choices = [('', 'جميع التصنيفات')] + [(c.id, c.name) for c in Category.query.filter_by(is_active=True).order_by(Category.name).all()]
        self.warehouse_id.choices = [('', 'جميع المستودعات')] + [(w.id, w.name) for w in Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()]

class BarcodeGeneratorForm(FlaskForm):
    product_id = SelectField('المنتج', coerce=int, validators=[DataRequired()],
                            render_kw={"class": "form-select"})
    copies = FloatField('عدد النسخ', validators=[DataRequired(), NumberRange(min=1, max=100)], default=1,
                       render_kw={"class": "form-control", "step": "1"})
    submit = SubmitField('طباعة الملصقات', render_kw={"class": "btn btn-primary"})
    
    def __init__(self, *args, **kwargs):
        super(BarcodeGeneratorForm, self).__init__(*args, **kwargs)
        
        # Populate choices
        self.product_id.choices = [(p.id, f"{p.name} - {p.barcode}") for p in Product.query.filter_by(is_active=True).order_by(Product.name).all()]
