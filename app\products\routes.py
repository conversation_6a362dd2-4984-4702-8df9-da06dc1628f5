from flask import render_template, redirect, url_for, flash, request, jsonify, send_file, current_app
from flask_login import login_required, current_user
from datetime import datetime
import os
from app import db
from app.products import bp
from app.products.forms import ProductForm, ProductSearchForm, BarcodeGeneratorForm
from app.models import Product, Category, Unit, Condition, Warehouse
from app.utils import can_edit_required, can_delete_required, save_picture, generate_barcode_number, create_barcode_image

@bp.route('/products')
@login_required
def products():
    form = ProductSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Product.query.filter_by(is_active=True)
    
    # Apply filters
    search = request.args.get('search', '', type=str)
    if search:
        query = query.filter(Product.name.contains(search) | 
                           Product.barcode.contains(search) |
                           Product.description.contains(search))
    
    category_id = request.args.get('category_id', '', type=str)
    if category_id and category_id.isdigit():
        query = query.filter_by(category_id=int(category_id))
    
    warehouse_id = request.args.get('warehouse_id', '', type=str)
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter_by(warehouse_id=int(warehouse_id))
    
    product_type = request.args.get('product_type', '', type=str)
    if product_type:
        query = query.filter_by(product_type=product_type)
    
    low_stock = request.args.get('low_stock', False, type=bool)
    if low_stock:
        query = query.filter(Product.quantity <= Product.min_quantity)
    
    products = query.order_by(Product.name).paginate(
        page=page, per_page=20, error_out=False)
    
    # Pre-fill form with current filters
    if request.method == 'GET':
        form.search.data = search
        if category_id and category_id.isdigit():
            form.category_id.data = int(category_id)
        if warehouse_id and warehouse_id.isdigit():
            form.warehouse_id.data = int(warehouse_id)
        form.product_type.data = product_type
        form.low_stock.data = low_stock
    
    return render_template('products/products.html', title='المنتجات', 
                         products=products, form=form)

@bp.route('/products/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_product():
    form = ProductForm()
    
    # Pre-fill barcode if provided in URL
    barcode_param = request.args.get('barcode', '')
    if barcode_param and request.method == 'GET':
        form.barcode.data = barcode_param
    
    if form.validate_on_submit():
        # Handle image upload
        image_path = None
        if form.image.data:
            image_path = save_picture(form.image.data, 'products')
        
        product = Product(
            barcode=form.barcode.data,
            name=form.name.data,
            description=form.description.data,
            category_id=form.category_id.data,
            unit_id=form.unit_id.data,
            condition_id=form.condition_id.data,
            warehouse_id=form.warehouse_id.data,
            quantity=form.quantity.data,
            min_quantity=form.min_quantity.data,
            product_type=form.product_type.data,
            image_path=image_path,
            notes=form.notes.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(product)
        db.session.commit()
        flash(f'تم إضافة المنتج {product.name} بنجاح', 'success')
        return redirect(url_for('products.products'))
    
    return render_template('products/product_form.html', title='إضافة منتج جديد', 
                         form=form, action='add')

@bp.route('/products/<int:id>')
@login_required
def product_detail(id):
    product = Product.query.get_or_404(id)
    
    # Generate barcode image
    barcode_image = create_barcode_image(product.barcode)
    
    return render_template('products/product_detail.html', title=f'تفاصيل المنتج - {product.name}',
                         product=product, barcode_image=barcode_image)

@bp.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_product(id):
    product = Product.query.get_or_404(id)
    form = ProductForm(original_product=product)
    
    if form.validate_on_submit():
        # Handle image upload
        if form.image.data:
            image_path = save_picture(form.image.data, 'products')
            product.image_path = image_path
        
        product.barcode = form.barcode.data
        product.name = form.name.data
        product.description = form.description.data
        product.category_id = form.category_id.data
        product.unit_id = form.unit_id.data
        product.condition_id = form.condition_id.data
        product.warehouse_id = form.warehouse_id.data
        product.quantity = form.quantity.data
        product.min_quantity = form.min_quantity.data
        product.product_type = form.product_type.data
        product.notes = form.notes.data
        product.is_active = form.is_active.data
        product.updated_at = datetime.utcnow()
        product.updated_by = current_user.id
        
        db.session.commit()
        flash(f'تم تحديث المنتج {product.name} بنجاح', 'success')
        return redirect(url_for('products.product_detail', id=product.id))
    
    elif request.method == 'GET':
        form.barcode.data = product.barcode
        form.name.data = product.name
        form.description.data = product.description
        form.category_id.data = product.category_id
        form.unit_id.data = product.unit_id
        form.condition_id.data = product.condition_id
        form.warehouse_id.data = product.warehouse_id
        form.quantity.data = product.quantity
        form.min_quantity.data = product.min_quantity
        form.product_type.data = product.product_type
        form.notes.data = product.notes
        form.is_active.data = product.is_active
    
    return render_template('products/product_form.html', title='تعديل المنتج', 
                         form=form, action='edit', product=product)

@bp.route('/products/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_product(id):
    product = Product.query.get_or_404(id)
    
    # Check if product is used in invoices
    if product.invoice_in_items or product.invoice_out_items:
        flash('لا يمكن حذف المنتج لأنه مستخدم في فواتير', 'warning')
        return redirect(url_for('products.products'))
    
    # Delete image file if exists
    if product.image_path:
        try:
            image_full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], product.image_path)
            if os.path.exists(image_full_path):
                os.remove(image_full_path)
        except:
            pass
    
    db.session.delete(product)
    db.session.commit()
    flash(f'تم حذف المنتج {product.name} بنجاح', 'success')
    return redirect(url_for('products.products'))

@bp.route('/products/generate-barcode')
@login_required
@can_edit_required
def generate_barcode():
    """Generate a random barcode number"""
    barcode = generate_barcode_number()
    return jsonify({'barcode': barcode})

@bp.route('/products/api/get-by-barcode/<barcode>')
@login_required
def get_product_by_barcode(barcode):
    """API endpoint to get product details by barcode"""
    product = Product.query.filter_by(barcode=barcode, is_active=True).first()
    
    if product:
        return jsonify({
            'success': True,
            'product': {
                'id': product.id,
                'name': product.name,
                'barcode': product.barcode,
                'quantity': product.quantity,
                'unit': product.unit_ref.name,
                'category': product.category_ref.name,
                'condition': product.condition_ref.name,
                'warehouse': product.warehouse_ref.name,
                'product_type': product.product_type
            }
        })
    else:
        return jsonify({'success': False, 'message': 'المنتج غير موجود'})

@bp.route('/products/barcode-labels', methods=['GET', 'POST'])
@login_required
def barcode_labels():
    form = BarcodeGeneratorForm()

    if form.validate_on_submit():
        product = Product.query.get_or_404(form.product_id.data)
        copies = int(form.copies.data)
        barcode_image = create_barcode_image(product.barcode)

        return render_template('products/barcode_labels_print.html',
                             title='طباعة ملصقات الباركود',
                             product=product, copies=copies, barcode_image=barcode_image)

    # Get recent products for quick access
    recent_products = Product.query.filter_by(is_active=True).order_by(Product.updated_at.desc()).limit(8).all()

    return render_template('products/barcode_labels.html',
                         title='طباعة ملصقات الباركود',
                         form=form, recent_products=recent_products)

# Categories routes (moved from warehouses)
@bp.route('/categories')
@login_required
def categories():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Category.query
    if search:
        query = query.filter(Category.name.contains(search))
    
    categories = query.order_by(Category.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/categories.html', title='التصنيفات',
                         categories=categories, search=search)

@bp.route('/categories/<int:id>/products')
@login_required
def category_products(id):
    category = Category.query.get_or_404(id)
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Product.query.filter_by(category_id=id, is_active=True)
    if search:
        query = query.filter(Product.name.contains(search) | 
                           Product.barcode.contains(search))
    
    products = query.order_by(Product.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('products/category_products.html', 
                         title=f'منتجات تصنيف {category.name}',
                         category=category, products=products, search=search)

# Units routes
@bp.route('/units')
@login_required
def units():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Unit.query
    if search:
        query = query.filter(Unit.name.contains(search) | Unit.symbol.contains(search))
    
    units = query.order_by(Unit.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/units.html', title='وحدات القياس',
                         units=units, search=search)

# Conditions routes
@bp.route('/conditions')
@login_required
def conditions():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Condition.query
    if search:
        query = query.filter(Condition.name.contains(search))
    
    conditions = query.order_by(Condition.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/conditions.html', title='حالة المواد',
                         conditions=conditions, search=search)

@bp.route('/conditions/<int:id>/products')
@login_required
def condition_products(id):
    condition = Condition.query.get_or_404(id)
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Product.query.filter_by(condition_id=id, is_active=True)
    if search:
        query = query.filter(Product.name.contains(search) | 
                           Product.barcode.contains(search))
    
    products = query.order_by(Product.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('products/condition_products.html', 
                         title=f'منتجات حالة {condition.name}',
                         condition=condition, products=products, search=search)
