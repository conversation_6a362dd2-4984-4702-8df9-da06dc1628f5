{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-{% if action == 'add' %}plus{% else %}edit{% endif %} me-2"></i>
            {% if action == 'add' %}إضافة عميل جديد{% else %}تعديل العميل{% endif %}
        </h1>
        {% if action == 'edit' %}
        <p class="text-muted">تعديل بيانات العميل: {{ customer.name }}</p>
        {% endif %}
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('warehouses.customers') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    بيانات العميل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="customer-form">
                    {{ form.hidden_tag() }}
                    
                    <!-- Customer Type -->
                    <div class="mb-3">
                        {{ form.customer_type.label(class="form-label required") }}
                        {{ form.customer_type(class="form-select" + (" is-invalid" if form.customer_type.errors else ""), id="customer_type") }}
                        {% if form.customer_type.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.customer_type.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Customer Name -->
                    <div class="mb-3">
                        {{ form.name.label(class="form-label required") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Company Name (for companies) -->
                    <div class="mb-3" id="company_name_field" style="display: none;">
                        {{ form.company_name.label(class="form-label") }}
                        {{ form.company_name(class="form-control" + (" is-invalid" if form.company_name.errors else "")) }}
                        {% if form.company_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.company_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Address -->
                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else "")) }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Tax Number (for companies) -->
                    <div class="mb-3" id="tax_number_field" style="display: none;">
                        {{ form.tax_number.label(class="form-label") }}
                        {{ form.tax_number(class="form-control" + (" is-invalid" if form.tax_number.errors else "")) }}
                        {% if form.tax_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.tax_number.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Notes -->
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Active Status -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input" + (" is-invalid" if form.is_active.errors else "")) }}
                            {{ form.is_active.label(class="form-check-label") }}
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-success btn-lg") }}
                            <a href="{{ url_for('warehouses.customers') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        {% if action == 'edit' %}
                        <div>
                            <a href="{{ url_for('warehouses.customer_detail', id=customer.id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Statistics (for edit mode) -->
        {% if action == 'edit' %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-1 text-primary">{{ customer.invoices_out|length }}</div>
                            <small class="text-muted">فاتورة بيع</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-1 text-success">{{ customer.total_purchases or 0 }}</div>
                            <small class="text-muted">إجمالي المشتريات</small>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        تم الإنشاء: {{ customer.created_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% if customer.updated_at %}
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-edit me-1"></i>
                        آخر تحديث: {{ customer.updated_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Help -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-user text-info me-2"></i>
                        <strong>النوع:</strong> اختر "فرد" للعملاء الأفراد أو "شركة" للشركات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-id-card text-warning me-2"></i>
                        <strong>الاسم:</strong> اسم العميل أو الشخص المسؤول في الشركة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-building text-success me-2"></i>
                        <strong>اسم الشركة:</strong> يظهر فقط للشركات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-receipt text-primary me-2"></i>
                        <strong>الرقم الضريبي:</strong> مطلوب للشركات فقط
                    </li>
                    <li>
                        <i class="fas fa-toggle-on text-danger me-2"></i>
                        <strong>الحالة:</strong> العملاء غير النشطين لا يظهرون في القوائم
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Invoices (for edit mode) -->
        {% if action == 'edit' and customer.recent_invoices %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    الفواتير الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for invoice in customer.recent_invoices[:5] %}
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">فاتورة #{{ invoice.invoice_number }}</div>
                                <small class="text-muted">{{ invoice.created_at.strftime('%Y-%m-%d') }}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">
                                {{ format_currency(invoice.total_amount) }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('invoices.invoices_out', customer_id=customer.id) }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الفواتير
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const customerTypeSelect = document.getElementById('customer_type');
    const companyNameField = document.getElementById('company_name_field');
    const taxNumberField = document.getElementById('tax_number_field');
    
    function toggleCompanyFields() {
        const isCompany = customerTypeSelect.value === 'company';
        companyNameField.style.display = isCompany ? 'block' : 'none';
        taxNumberField.style.display = isCompany ? 'block' : 'none';
        
        // Update required attributes
        const companyNameInput = companyNameField.querySelector('input');
        if (companyNameInput) {
            if (isCompany) {
                companyNameInput.setAttribute('required', 'required');
            } else {
                companyNameInput.removeAttribute('required');
            }
        }
    }
    
    // Initial toggle
    toggleCompanyFields();
    
    // Toggle on change
    customerTypeSelect.addEventListener('change', toggleCompanyFields);
    
    // Form validation
    const form = document.getElementById('customer-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm('customer-form')) {
                e.preventDefault();
            }
        });
    }
    
    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Remove non-digits
            let value = e.target.value.replace(/\D/g, '');
            
            // Format as phone number
            if (value.length >= 10) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
            }
            
            e.target.value = value;
        });
    }
});
</script>
{% endblock %}
