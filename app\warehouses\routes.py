from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app import db
from app.warehouses import bp
from app.warehouses.forms import WarehouseForm, CategoryForm, UnitForm, ConditionForm, CustomerForm, SupplierForm
from app.models import Warehouse, Category, Unit, Condition, Customer, Supplier, Product
from app.utils import can_edit_required, can_delete_required

# Warehouses
@bp.route('/warehouses')
@login_required
def warehouses():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Warehouse.query
    if search:
        query = query.filter(Warehouse.name.contains(search) | 
                           Warehouse.location.contains(search))
    
    warehouses = query.order_by(Warehouse.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/warehouses.html', title='المستودعات', 
                         warehouses=warehouses, search=search)

@bp.route('/warehouses/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_warehouse():
    form = WarehouseForm()
    if form.validate_on_submit():
        warehouse = Warehouse(
            name=form.name.data,
            location=form.location.data,
            description=form.description.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(warehouse)
        db.session.commit()
        flash(f'تم إضافة المستودع {warehouse.name} بنجاح', 'success')
        return redirect(url_for('warehouses.warehouses'))
    
    return render_template('warehouses/warehouse_form.html', title='إضافة مستودع جديد', 
                         form=form, action='add')

@bp.route('/warehouses/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_warehouse(id):
    warehouse = Warehouse.query.get_or_404(id)
    form = WarehouseForm()
    
    if form.validate_on_submit():
        warehouse.name = form.name.data
        warehouse.location = form.location.data
        warehouse.description = form.description.data
        warehouse.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث المستودع {warehouse.name} بنجاح', 'success')
        return redirect(url_for('warehouses.warehouses'))
    
    elif request.method == 'GET':
        form.name.data = warehouse.name
        form.location.data = warehouse.location
        form.description.data = warehouse.description
        form.is_active.data = warehouse.is_active
    
    return render_template('warehouses/warehouse_form.html', title='تعديل المستودع', 
                         form=form, action='edit', warehouse=warehouse)

@bp.route('/warehouses/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_warehouse(id):
    warehouse = Warehouse.query.get_or_404(id)
    
    # Check if warehouse has products
    if warehouse.products:
        flash('لا يمكن حذف المستودع لأنه يحتوي على منتجات', 'warning')
        return redirect(url_for('warehouses.warehouses'))
    
    db.session.delete(warehouse)
    db.session.commit()
    flash(f'تم حذف المستودع {warehouse.name} بنجاح', 'success')
    return redirect(url_for('warehouses.warehouses'))

@bp.route('/warehouses/<int:id>/products')
@login_required
def warehouse_products(id):
    warehouse = Warehouse.query.get_or_404(id)
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Product.query.filter_by(warehouse_id=id, is_active=True)
    if search:
        query = query.filter(Product.name.contains(search) | 
                           Product.barcode.contains(search))
    
    products = query.order_by(Product.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/warehouse_products.html', 
                         title=f'منتجات مستودع {warehouse.name}',
                         warehouse=warehouse, products=products, search=search)

# Categories
@bp.route('/categories')
@login_required
def categories():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Category.query
    if search:
        query = query.filter(Category.name.contains(search))
    
    categories = query.order_by(Category.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/categories.html', title='التصنيفات', 
                         categories=categories, search=search)

@bp.route('/categories/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_category():
    form = CategoryForm()
    if form.validate_on_submit():
        category = Category(
            name=form.name.data,
            description=form.description.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(category)
        db.session.commit()
        flash(f'تم إضافة التصنيف {category.name} بنجاح', 'success')
        return redirect(url_for('warehouses.categories'))
    
    return render_template('warehouses/category_form.html', title='إضافة تصنيف جديد', 
                         form=form, action='add')

@bp.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_category(id):
    category = Category.query.get_or_404(id)
    form = CategoryForm()
    
    if form.validate_on_submit():
        category.name = form.name.data
        category.description = form.description.data
        category.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث التصنيف {category.name} بنجاح', 'success')
        return redirect(url_for('warehouses.categories'))
    
    elif request.method == 'GET':
        form.name.data = category.name
        form.description.data = category.description
        form.is_active.data = category.is_active
    
    return render_template('warehouses/category_form.html', title='تعديل التصنيف', 
                         form=form, action='edit', category=category)

@bp.route('/categories/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_category(id):
    category = Category.query.get_or_404(id)
    
    # Check if category has products
    if category.products:
        flash('لا يمكن حذف التصنيف لأنه يحتوي على منتجات', 'warning')
        return redirect(url_for('warehouses.categories'))
    
    db.session.delete(category)
    db.session.commit()
    flash(f'تم حذف التصنيف {category.name} بنجاح', 'success')
    return redirect(url_for('warehouses.categories'))

# Units
@bp.route('/units')
@login_required
def units():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Unit.query
    if search:
        query = query.filter(Unit.name.contains(search) | Unit.symbol.contains(search))
    
    units = query.order_by(Unit.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/units.html', title='وحدات القياس', 
                         units=units, search=search)

@bp.route('/units/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_unit():
    form = UnitForm()
    if form.validate_on_submit():
        unit = Unit(
            name=form.name.data,
            symbol=form.symbol.data,
            description=form.description.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(unit)
        db.session.commit()
        flash(f'تم إضافة وحدة القياس {unit.name} بنجاح', 'success')
        return redirect(url_for('warehouses.units'))
    
    return render_template('warehouses/unit_form.html', title='إضافة وحدة قياس جديدة', 
                         form=form, action='add')

@bp.route('/units/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_unit(id):
    unit = Unit.query.get_or_404(id)
    form = UnitForm()
    
    if form.validate_on_submit():
        unit.name = form.name.data
        unit.symbol = form.symbol.data
        unit.description = form.description.data
        unit.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث وحدة القياس {unit.name} بنجاح', 'success')
        return redirect(url_for('warehouses.units'))
    
    elif request.method == 'GET':
        form.name.data = unit.name
        form.symbol.data = unit.symbol
        form.description.data = unit.description
        form.is_active.data = unit.is_active
    
    return render_template('warehouses/unit_form.html', title='تعديل وحدة القياس', 
                         form=form, action='edit', unit=unit)

@bp.route('/units/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_unit(id):
    unit = Unit.query.get_or_404(id)
    
    # Check if unit has products
    if unit.products:
        flash('لا يمكن حذف وحدة القياس لأنها مستخدمة في منتجات', 'warning')
        return redirect(url_for('warehouses.units'))
    
    db.session.delete(unit)
    db.session.commit()
    flash(f'تم حذف وحدة القياس {unit.name} بنجاح', 'success')
    return redirect(url_for('warehouses.units'))

# Conditions
@bp.route('/conditions')
@login_required
def conditions():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Condition.query
    if search:
        query = query.filter(Condition.name.contains(search))
    
    conditions = query.order_by(Condition.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/conditions.html', title='حالة المواد', 
                         conditions=conditions, search=search)

@bp.route('/conditions/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_condition():
    form = ConditionForm()
    if form.validate_on_submit():
        condition = Condition(
            name=form.name.data,
            description=form.description.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(condition)
        db.session.commit()
        flash(f'تم إضافة حالة المادة {condition.name} بنجاح', 'success')
        return redirect(url_for('warehouses.conditions'))
    
    return render_template('warehouses/condition_form.html', title='إضافة حالة مادة جديدة', 
                         form=form, action='add')

@bp.route('/conditions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_condition(id):
    condition = Condition.query.get_or_404(id)
    form = ConditionForm()
    
    if form.validate_on_submit():
        condition.name = form.name.data
        condition.description = form.description.data
        condition.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث حالة المادة {condition.name} بنجاح', 'success')
        return redirect(url_for('warehouses.conditions'))
    
    elif request.method == 'GET':
        form.name.data = condition.name
        form.description.data = condition.description
        form.is_active.data = condition.is_active
    
    return render_template('warehouses/condition_form.html', title='تعديل حالة المادة', 
                         form=form, action='edit', condition=condition)

@bp.route('/conditions/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_condition(id):
    condition = Condition.query.get_or_404(id)
    
    # Check if condition has products
    if condition.products:
        flash('لا يمكن حذف حالة المادة لأنها مستخدمة في منتجات', 'warning')
        return redirect(url_for('warehouses.conditions'))
    
    db.session.delete(condition)
    db.session.commit()
    flash(f'تم حذف حالة المادة {condition.name} بنجاح', 'success')
    return redirect(url_for('warehouses.conditions'))

# Customers
@bp.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Customer.query
    if search:
        query = query.filter(Customer.name.contains(search) | 
                           Customer.phone.contains(search) |
                           Customer.email.contains(search))
    
    customers = query.order_by(Customer.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/customers.html', title='العملاء', 
                         customers=customers, search=search)

@bp.route('/customers/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_customer():
    form = CustomerForm()
    if form.validate_on_submit():
        customer = Customer(
            name=form.name.data,
            phone=form.phone.data,
            email=form.email.data,
            address=form.address.data,
            tax_number=form.tax_number.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(customer)
        db.session.commit()
        flash(f'تم إضافة العميل {customer.name} بنجاح', 'success')
        return redirect(url_for('warehouses.customers'))
    
    return render_template('warehouses/customer_form.html', title='إضافة عميل جديد', 
                         form=form, action='add')

@bp.route('/customers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_customer(id):
    customer = Customer.query.get_or_404(id)
    form = CustomerForm()
    
    if form.validate_on_submit():
        customer.name = form.name.data
        customer.phone = form.phone.data
        customer.email = form.email.data
        customer.address = form.address.data
        customer.tax_number = form.tax_number.data
        customer.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث العميل {customer.name} بنجاح', 'success')
        return redirect(url_for('warehouses.customers'))
    
    elif request.method == 'GET':
        form.name.data = customer.name
        form.phone.data = customer.phone
        form.email.data = customer.email
        form.address.data = customer.address
        form.tax_number.data = customer.tax_number
        form.is_active.data = customer.is_active
    
    return render_template('warehouses/customer_form.html', title='تعديل العميل', 
                         form=form, action='edit', customer=customer)

@bp.route('/customers/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_customer(id):
    customer = Customer.query.get_or_404(id)
    
    # Check if customer has invoices
    if customer.invoices_out:
        flash('لا يمكن حذف العميل لأنه يحتوي على فواتير', 'warning')
        return redirect(url_for('warehouses.customers'))
    
    db.session.delete(customer)
    db.session.commit()
    flash(f'تم حذف العميل {customer.name} بنجاح', 'success')
    return redirect(url_for('warehouses.customers'))

# Suppliers
@bp.route('/suppliers')
@login_required
def suppliers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Supplier.query
    if search:
        query = query.filter(Supplier.name.contains(search) | 
                           Supplier.phone.contains(search) |
                           Supplier.email.contains(search))
    
    suppliers = query.order_by(Supplier.name).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('warehouses/suppliers.html', title='الموردين', 
                         suppliers=suppliers, search=search)

@bp.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
@can_edit_required
def add_supplier():
    form = SupplierForm()
    if form.validate_on_submit():
        supplier = Supplier(
            name=form.name.data,
            phone=form.phone.data,
            email=form.email.data,
            address=form.address.data,
            tax_number=form.tax_number.data,
            is_active=form.is_active.data,
            created_at=datetime.utcnow(),
            created_by=current_user.id
        )
        db.session.add(supplier)
        db.session.commit()
        flash(f'تم إضافة المورد {supplier.name} بنجاح', 'success')
        return redirect(url_for('warehouses.suppliers'))
    
    return render_template('warehouses/supplier_form.html', title='إضافة مورد جديد', 
                         form=form, action='add')

@bp.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@can_edit_required
def edit_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    form = SupplierForm()
    
    if form.validate_on_submit():
        supplier.name = form.name.data
        supplier.phone = form.phone.data
        supplier.email = form.email.data
        supplier.address = form.address.data
        supplier.tax_number = form.tax_number.data
        supplier.is_active = form.is_active.data
        db.session.commit()
        flash(f'تم تحديث المورد {supplier.name} بنجاح', 'success')
        return redirect(url_for('warehouses.suppliers'))
    
    elif request.method == 'GET':
        form.name.data = supplier.name
        form.phone.data = supplier.phone
        form.email.data = supplier.email
        form.address.data = supplier.address
        form.tax_number.data = supplier.tax_number
        form.is_active.data = supplier.is_active
    
    return render_template('warehouses/supplier_form.html', title='تعديل المورد', 
                         form=form, action='edit', supplier=supplier)

@bp.route('/suppliers/<int:id>/delete', methods=['POST'])
@login_required
@can_delete_required
def delete_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    
    # Check if supplier has invoices
    if supplier.invoices_in:
        flash('لا يمكن حذف المورد لأنه يحتوي على فواتير', 'warning')
        return redirect(url_for('warehouses.suppliers'))
    
    db.session.delete(supplier)
    db.session.commit()
    flash(f'تم حذف المورد {supplier.name} بنجاح', 'success')
    return redirect(url_for('warehouses.suppliers')
