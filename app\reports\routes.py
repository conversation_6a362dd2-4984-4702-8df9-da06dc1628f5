from flask import render_template, redirect, url_for, flash, request, make_response, send_file
from flask_login import login_required, current_user
from datetime import datetime, timezone
from app import db
from app.reports import bp
from app.models import (InvoiceIn, InvoiceOut, Product, Supplier, Customer, 
                       Warehouse, Category, Unit, Condition, User)
import io
import csv
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import xlsxwriter


@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للتقارير"""
    return render_template('reports/index.html')


@bp.route('/inventory_report')
@login_required
def inventory_report():
    """تقرير المخزون"""
    warehouse_id = request.args.get('warehouse_id', type=int)
    category_id = request.args.get('category_id', type=int)
    low_stock = request.args.get('low_stock', type=bool)
    export_format = request.args.get('export', '')
    
    query = Product.query.filter_by(is_active=True)
    
    if warehouse_id:
        # Filter by warehouse (this would need warehouse-specific stock tracking)
        pass
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if low_stock:
        # Filter products with low stock (assuming stock_quantity field exists)
        query = query.filter(Product.stock_quantity < 10)
    
    products = query.order_by(Product.name).all()
    
    # Get filter options
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
    
    if export_format == 'excel':
        return export_inventory_excel(products)
    elif export_format == 'pdf':
        return export_inventory_pdf(products)
    
    return render_template('reports/inventory_report.html',
                         products=products,
                         warehouses=warehouses,
                         categories=categories)


@bp.route('/sales_report')
@login_required
def sales_report():
    """تقرير المبيعات"""
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    customer_id = request.args.get('customer_id', type=int)
    warehouse_id = request.args.get('warehouse_id', type=int)
    export_format = request.args.get('export', '')
    
    query = InvoiceOut.query
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    if customer_id:
        query = query.filter_by(customer_id=customer_id)
    
    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)
    
    invoices = query.order_by(InvoiceOut.invoice_date.desc()).all()
    
    # Calculate totals
    total_amount = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)
    
    # Get filter options
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    
    if export_format == 'excel':
        return export_sales_excel(invoices)
    elif export_format == 'pdf':
        return export_sales_pdf(invoices)
    
    return render_template('reports/sales_report.html',
                         invoices=invoices,
                         customers=customers,
                         warehouses=warehouses,
                         total_amount=total_amount,
                         total_invoices=total_invoices)


@bp.route('/purchases_report')
@login_required
def purchases_report():
    """تقرير المشتريات"""
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    supplier_id = request.args.get('supplier_id', type=int)
    warehouse_id = request.args.get('warehouse_id', type=int)
    export_format = request.args.get('export', '')
    
    query = InvoiceIn.query
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)
    
    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)
    
    invoices = query.order_by(InvoiceIn.invoice_date.desc()).all()
    
    # Calculate totals
    total_amount = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)
    
    # Get filter options
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()
    
    if export_format == 'excel':
        return export_purchases_excel(invoices)
    elif export_format == 'pdf':
        return export_purchases_pdf(invoices)
    
    return render_template('reports/purchases_report.html',
                         invoices=invoices,
                         suppliers=suppliers,
                         warehouses=warehouses,
                         total_amount=total_amount,
                         total_invoices=total_invoices)


@bp.route('/customer_statement/<int:customer_id>')
@login_required
def customer_statement(customer_id):
    """كشف حساب العميل"""
    customer = Customer.query.get_or_404(customer_id)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    export_format = request.args.get('export', '')
    
    query = InvoiceOut.query.filter_by(customer_id=customer_id)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceOut.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    invoices = query.order_by(InvoiceOut.invoice_date.desc()).all()
    
    # Calculate totals
    total_amount = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)
    
    if export_format == 'pdf':
        return export_customer_statement_pdf(customer, invoices)
    
    return render_template('reports/customer_statement.html',
                         customer=customer,
                         invoices=invoices,
                         total_amount=total_amount,
                         total_invoices=total_invoices)


@bp.route('/supplier_statement/<int:supplier_id>')
@login_required
def supplier_statement(supplier_id):
    """كشف حساب المورد"""
    supplier = Supplier.query.get_or_404(supplier_id)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    export_format = request.args.get('export', '')
    
    query = InvoiceIn.query.filter_by(supplier_id=supplier_id)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(InvoiceIn.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    invoices = query.order_by(InvoiceIn.invoice_date.desc()).all()
    
    # Calculate totals
    total_amount = sum(invoice.total_amount for invoice in invoices)
    total_invoices = len(invoices)
    
    if export_format == 'pdf':
        return export_supplier_statement_pdf(supplier, invoices)
    
    return render_template('reports/supplier_statement.html',
                         supplier=supplier,
                         invoices=invoices,
                         total_amount=total_amount,
                         total_invoices=total_invoices)


@bp.route('/product_movement/<int:product_id>')
@login_required
def product_movement(product_id):
    """تقرير حركة المنتج"""
    product = Product.query.get_or_404(product_id)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Get product movements from invoices
    movements = []
    
    # Purchase invoices (incoming)
    purchase_items = db.session.query(InvoiceInItem).join(InvoiceIn).filter(
        InvoiceInItem.product_id == product_id
    )
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            purchase_items = purchase_items.filter(InvoiceIn.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            purchase_items = purchase_items.filter(InvoiceIn.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    for item in purchase_items.all():
        movements.append({
            'date': item.invoice.invoice_date,
            'type': 'شراء',
            'reference': item.invoice.invoice_number,
            'quantity_in': item.quantity,
            'quantity_out': 0,
            'balance': 0,  # Will be calculated
            'notes': f'من المورد: {item.invoice.supplier.name}'
        })
    
    # Sales invoices (outgoing)
    sales_items = db.session.query(InvoiceOutItem).join(InvoiceOut).filter(
        InvoiceOutItem.product_id == product_id
    )
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            sales_items = sales_items.filter(InvoiceOut.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            sales_items = sales_items.filter(InvoiceOut.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    for item in sales_items.all():
        movements.append({
            'date': item.invoice.invoice_date,
            'type': 'بيع',
            'reference': item.invoice.invoice_number,
            'quantity_in': 0,
            'quantity_out': item.quantity,
            'balance': 0,  # Will be calculated
            'notes': f'للعميل: {item.invoice.customer.name}'
        })
    
    # Sort by date
    movements.sort(key=lambda x: x['date'])
    
    # Calculate running balance
    balance = 0
    for movement in movements:
        balance += movement['quantity_in'] - movement['quantity_out']
        movement['balance'] = balance
    
    return render_template('reports/product_movement.html',
                         product=product,
                         movements=movements)


def export_inventory_excel(products):
    """تصدير تقرير المخزون إلى Excel"""
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('تقرير المخزون')
    
    # Add headers
    headers = ['اسم المنتج', 'الباركود', 'التصنيف', 'الوحدة', 'الكمية المتوفرة', 'الحد الأدنى']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)
    
    # Add data
    for row, product in enumerate(products, 1):
        worksheet.write(row, 0, product.name)
        worksheet.write(row, 1, product.barcode)
        worksheet.write(row, 2, product.category_ref.name if product.category_ref else '')
        worksheet.write(row, 3, product.unit_ref.name if product.unit_ref else '')
        worksheet.write(row, 4, getattr(product, 'stock_quantity', 0))
        worksheet.write(row, 5, getattr(product, 'min_stock', 0))
    
    workbook.close()
    output.seek(0)
    
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=inventory_report.xlsx'
    
    return response


def export_inventory_pdf(products):
    """تصدير تقرير المخزون إلى PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    elements = []
    
    # Add title
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    title = Paragraph("تقرير المخزون", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Create table data
    data = [['اسم المنتج', 'الباركود', 'التصنيف', 'الوحدة', 'الكمية']]
    
    for product in products:
        data.append([
            product.name,
            product.barcode,
            product.category_ref.name if product.category_ref else '',
            product.unit_ref.name if product.unit_ref else '',
            str(getattr(product, 'stock_quantity', 0))
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    doc.build(elements)
    
    buffer.seek(0)
    response = make_response(buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=inventory_report.pdf'
    
    return response


def export_sales_excel(invoices):
    """تصدير تقرير المبيعات إلى Excel"""
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('تقرير المبيعات')
    
    # Add headers
    headers = ['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ الإجمالي', 'الحالة']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)
    
    # Add data
    for row, invoice in enumerate(invoices, 1):
        worksheet.write(row, 0, invoice.invoice_number)
        worksheet.write(row, 1, invoice.invoice_date.strftime('%Y-%m-%d'))
        worksheet.write(row, 2, invoice.customer.name)
        worksheet.write(row, 3, invoice.total_amount)
        worksheet.write(row, 4, invoice.status)
    
    workbook.close()
    output.seek(0)
    
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=sales_report.xlsx'
    
    return response


def export_sales_pdf(invoices):
    """تصدير تقرير المبيعات إلى PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    elements = []
    
    # Add title
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    title = Paragraph("تقرير المبيعات", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Create table data
    data = [['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ', 'الحالة']]
    
    for invoice in invoices:
        data.append([
            invoice.invoice_number,
            invoice.invoice_date.strftime('%Y-%m-%d'),
            invoice.customer.name,
            f"{invoice.total_amount:.2f}",
            invoice.status
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    doc.build(elements)
    
    buffer.seek(0)
    response = make_response(buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=sales_report.pdf'
    
    return response


def export_purchases_excel(invoices):
    """تصدير تقرير المشتريات إلى Excel"""
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('تقرير المشتريات')
    
    # Add headers
    headers = ['رقم الفاتورة', 'التاريخ', 'المورد', 'المبلغ الإجمالي', 'الحالة']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)
    
    # Add data
    for row, invoice in enumerate(invoices, 1):
        worksheet.write(row, 0, invoice.invoice_number)
        worksheet.write(row, 1, invoice.invoice_date.strftime('%Y-%m-%d'))
        worksheet.write(row, 2, invoice.supplier.name)
        worksheet.write(row, 3, invoice.total_amount)
        worksheet.write(row, 4, invoice.status)
    
    workbook.close()
    output.seek(0)
    
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=purchases_report.xlsx'
    
    return response


def export_purchases_pdf(invoices):
    """تصدير تقرير المشتريات إلى PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    elements = []
    
    # Add title
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    title = Paragraph("تقرير المشتريات", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Create table data
    data = [['رقم الفاتورة', 'التاريخ', 'المورد', 'المبلغ', 'الحالة']]
    
    for invoice in invoices:
        data.append([
            invoice.invoice_number,
            invoice.invoice_date.strftime('%Y-%m-%d'),
            invoice.supplier.name,
            f"{invoice.total_amount:.2f}",
            invoice.status
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    doc.build(elements)
    
    buffer.seek(0)
    response = make_response(buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=purchases_report.pdf'
    
    return response


def export_customer_statement_pdf(customer, invoices):
    """تصدير كشف حساب العميل إلى PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    elements = []
    
    # Add title
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    title = Paragraph(f"كشف حساب العميل: {customer.name}", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Create table data
    data = [['رقم الفاتورة', 'التاريخ', 'المبلغ', 'الحالة']]
    
    for invoice in invoices:
        data.append([
            invoice.invoice_number,
            invoice.invoice_date.strftime('%Y-%m-%d'),
            f"{invoice.total_amount:.2f}",
            invoice.status
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    doc.build(elements)
    
    buffer.seek(0)
    response = make_response(buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=customer_statement_{customer.id}.pdf'
    
    return response


def export_supplier_statement_pdf(supplier, invoices):
    """تصدير كشف حساب المورد إلى PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    elements = []
    
    # Add title
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    title = Paragraph(f"كشف حساب المورد: {supplier.name}", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Create table data
    data = [['رقم الفاتورة', 'التاريخ', 'المبلغ', 'الحالة']]
    
    for invoice in invoices:
        data.append([
            invoice.invoice_number,
            invoice.invoice_date.strftime('%Y-%m-%d'),
            f"{invoice.total_amount:.2f}",
            invoice.status
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    doc.build(elements)
    
    buffer.seek(0)
    response = make_response(buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=supplier_statement_{supplier.id}.pdf'
    
    return response
