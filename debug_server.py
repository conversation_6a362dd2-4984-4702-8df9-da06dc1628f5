#!/usr/bin/env python3
"""
تشغيل خادم نظام إدارة المستودعات مع تفاصيل الأخطاء
"""

import sys
import traceback
from app import create_app

def main():
    """تشغيل التطبيق مع معالجة الأخطاء"""
    try:
        print("إنشاء التطبيق...")
        app = create_app()
        print("تم إنشاء التطبيق بنجاح!")
        
        print("بدء تشغيل الخادم...")
        print("الخادم متاح على: http://127.0.0.1:5005")
        
        # تفعيل وضع التطوير مع عرض الأخطاء
        app.config['DEBUG'] = True
        app.config['TESTING'] = True

        app.run(
            host='127.0.0.1',
            port=5005,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"خطأ في الاستيراد: {e}")
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"خطأ عام: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
