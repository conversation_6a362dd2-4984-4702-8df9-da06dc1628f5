{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-{% if action == 'add' %}plus{% else %}edit{% endif %} me-2"></i>
            {% if action == 'add' %}إضافة فاتورة شراء جديدة{% else %}تعديل فاتورة الشراء{% endif %}
        </h1>
        {% if action == 'edit' %}
        <p class="text-muted">تعديل فاتورة رقم: {{ invoice.invoice_number }}</p>
        {% endif %}
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('invoices.invoices_in') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<form method="POST" id="invoice-form">
    {{ form.hidden_tag() }}
    
    <!-- Invoice Header -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                بيانات الفاتورة
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    {{ form.invoice_number.label(class="form-label required") }}
                    {{ form.invoice_number(class="form-control" + (" is-invalid" if form.invoice_number.errors else "")) }}
                    {% if form.invoice_number.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.invoice_number.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-3 mb-3">
                    {{ form.reference_number.label(class="form-label") }}
                    {{ form.reference_number(class="form-control" + (" is-invalid" if form.reference_number.errors else "")) }}
                    {% if form.reference_number.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.reference_number.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-3 mb-3">
                    {{ form.invoice_date.label(class="form-label required") }}
                    {{ form.invoice_date(class="form-control" + (" is-invalid" if form.invoice_date.errors else "")) }}
                    {% if form.invoice_date.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.invoice_date.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-3 mb-3">
                    {{ form.supplier_id.label(class="form-label required") }}
                    {{ form.supplier_id(class="form-select" + (" is-invalid" if form.supplier_id.errors else "")) }}
                    {% if form.supplier_id.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.supplier_id.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    {{ form.warehouse_id.label(class="form-label required") }}
                    {{ form.warehouse_id(class="form-select" + (" is-invalid" if form.warehouse_id.errors else "")) }}
                    {% if form.warehouse_id.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.warehouse_id.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    {{ form.status.label(class="form-label") }}
                    {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                    {% if form.status.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.status.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="mb-3">
                {{ form.notes.label(class="form-label") }}
                {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                {% if form.notes.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.notes.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Barcode Scanner Section -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-barcode me-2"></i>
                إضافة المنتجات
            </h5>
            <button type="button" class="btn btn-primary btn-sm" id="toggle-scanner">
                <i class="fas fa-camera me-2"></i>
                تشغيل الماسح
            </button>
        </div>
        <div class="card-body">
            <!-- Barcode Scanner -->
            <div id="scanner-section" style="display: none;">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <video id="barcode-scanner" style="width: 100%; max-height: 300px; border: 1px solid #ddd;"></video>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            وجه الكاميرا نحو الباركود لمسحه تلقائياً
                        </div>
                        <div id="scanner-result" class="alert alert-success" style="display: none;">
                            <i class="fas fa-check me-2"></i>
                            تم مسح الباركود: <span id="scanned-barcode"></span>
                        </div>
                    </div>
                </div>
                <hr>
            </div>
            
            <!-- Manual Product Entry -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">الباركود</label>
                    <input type="text" id="product-barcode" class="form-control" placeholder="امسح أو أدخل الباركود">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">المنتج</label>
                    <select id="product-select" class="form-select">
                        <option value="">اختر المنتج</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" data-barcode="{{ product.barcode }}" 
                                data-unit="{{ product.unit_ref.name }}" data-category="{{ product.category_ref.name }}">
                            {{ product.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">الكمية</label>
                    <input type="number" id="product-quantity" class="form-control" min="1" value="1" step="0.01">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">سعر الوحدة</label>
                    <input type="number" id="product-price" class="form-control" min="0" step="0.01">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" id="add-product">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" class="btn btn-info" id="add-new-product" style="display: none;">
                            <i class="fas fa-plus-circle"></i>
                            جديد
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Product Details Display -->
            <div id="product-details" class="alert alert-info" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <strong>المنتج:</strong> <span id="detail-name"></span>
                    </div>
                    <div class="col-md-2">
                        <strong>الوحدة:</strong> <span id="detail-unit"></span>
                    </div>
                    <div class="col-md-2">
                        <strong>التصنيف:</strong> <span id="detail-category"></span>
                    </div>
                    <div class="col-md-2">
                        <strong>الحالة:</strong> <span id="detail-condition"></span>
                    </div>
                    <div class="col-md-3">
                        <strong>المخزون الحالي:</strong> <span id="detail-stock"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invoice Items -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                أصناف الفاتورة
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="invoice-items-table">
                    <thead class="table-light">
                        <tr>
                            <th>المنتج</th>
                            <th>الباركود</th>
                            <th>الوحدة</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الإجمالي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="invoice-items-body">
                        {% if action == 'edit' and invoice.items %}
                        {% for item in invoice.items %}
                        <tr data-product-id="{{ item.product_id }}">
                            <td>{{ item.product_ref.name }}</td>
                            <td>{{ item.product_ref.barcode }}</td>
                            <td>{{ item.product_ref.unit_ref.name }}</td>
                            <td>
                                <input type="number" class="form-control item-quantity" value="{{ item.quantity }}" 
                                       min="1" step="0.01" style="width: 100px;">
                            </td>
                            <td>
                                <input type="number" class="form-control item-price" value="{{ item.unit_price }}" 
                                       min="0" step="0.01" style="width: 120px;">
                            </td>
                            <td class="item-total">{{ format_currency(item.total_price) }}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% endif %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5" class="text-end">الإجمالي:</th>
                            <th id="total-amount">0.00 ر.س</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <div id="no-items" class="text-center py-4" {% if action == 'edit' and invoice.items %}style="display: none;"{% endif %}>
                <i class="fas fa-box-open fa-2x text-muted mb-3"></i>
                <p class="text-muted">لم يتم إضافة أي أصناف بعد</p>
                <p class="text-muted">استخدم ماسح الباركود أو اختر المنتجات يدوياً</p>
            </div>
        </div>
    </div>
    
    <!-- Form Actions -->
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <div>
                    {{ form.submit(class="btn btn-success btn-lg", id="submit-btn") }}
                    <a href="{{ url_for('invoices.invoices_in') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                </div>
                {% if action == 'edit' %}
                <div>
                    <a href="{{ url_for('invoices.invoice_in_detail', id=invoice.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{{ url_for('invoices.print_invoice_in', id=invoice.id) }}" class="btn btn-outline-primary ms-2" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</form>

<!-- Hidden inputs for invoice items -->
<div id="hidden-items"></div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
let invoiceItems = [];
let scannerActive = false;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize existing items for edit mode
    {% if action == 'edit' and invoice.items %}
    {% for item in invoice.items %}
    invoiceItems.push({
        product_id: {{ item.product_id }},
        name: "{{ item.product_ref.name }}",
        barcode: "{{ item.product_ref.barcode }}",
        unit: "{{ item.product_ref.unit_ref.name }}",
        quantity: {{ item.quantity }},
        price: {{ item.unit_price }}
    });
    {% endfor %}
    {% endif %}
    
    updateTotal();
    
    // Barcode scanner toggle
    document.getElementById('toggle-scanner').addEventListener('click', function() {
        const scannerSection = document.getElementById('scanner-section');
        if (scannerActive) {
            stopScanner();
            scannerSection.style.display = 'none';
            this.innerHTML = '<i class="fas fa-camera me-2"></i>تشغيل الماسح';
        } else {
            startScanner();
            scannerSection.style.display = 'block';
            this.innerHTML = '<i class="fas fa-stop me-2"></i>إيقاف الماسح';
        }
    });
    
    // Product selection and barcode input
    document.getElementById('product-barcode').addEventListener('input', function() {
        const barcode = this.value;
        if (barcode) {
            findProductByBarcode(barcode);
        }
    });
    
    document.getElementById('product-select').addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            document.getElementById('product-barcode').value = option.dataset.barcode;
            showProductDetails(option.value);
        }
    });
    
    // Add product button
    document.getElementById('add-product').addEventListener('click', addProduct);
    
    // Form submission
    document.getElementById('invoice-form').addEventListener('submit', function(e) {
        if (invoiceItems.length === 0) {
            e.preventDefault();
            alert('يجب إضافة منتج واحد على الأقل');
            return;
        }
        
        // Add hidden inputs for items
        const hiddenItemsDiv = document.getElementById('hidden-items');
        hiddenItemsDiv.innerHTML = '';
        
        invoiceItems.forEach((item, index) => {
            ['product_id', 'quantity', 'price'].forEach(field => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = `items[${index}][${field}]`;
                input.value = item[field === 'price' ? 'price' : field];
                hiddenItemsDiv.appendChild(input);
            });
        });
    });
});

function startScanner() {
    Quagga.init({
        inputStream: {
            name: "Live",
            type: "LiveStream",
            target: document.querySelector('#barcode-scanner')
        },
        decoder: {
            readers: ["ean_reader", "ean_8_reader", "code_128_reader"]
        }
    }, function(err) {
        if (err) {
            console.log(err);
            return;
        }
        Quagga.start();
        scannerActive = true;
    });
    
    Quagga.onDetected(function(data) {
        const barcode = data.codeResult.code;
        document.getElementById('scanned-barcode').textContent = barcode;
        document.getElementById('scanner-result').style.display = 'block';
        document.getElementById('product-barcode').value = barcode;
        findProductByBarcode(barcode);
    });
}

function stopScanner() {
    if (scannerActive) {
        Quagga.stop();
        scannerActive = false;
        document.getElementById('scanner-result').style.display = 'none';
    }
}

function findProductByBarcode(barcode) {
    const productSelect = document.getElementById('product-select');
    const options = productSelect.options;
    
    for (let i = 0; i < options.length; i++) {
        if (options[i].dataset.barcode === barcode) {
            productSelect.selectedIndex = i;
            showProductDetails(options[i].value);
            return;
        }
    }
    
    // Product not found - show add new product button
    document.getElementById('add-new-product').style.display = 'block';
    document.getElementById('product-details').style.display = 'none';
}

function showProductDetails(productId) {
    // This would typically fetch product details via AJAX
    // For now, we'll use the data attributes
    const option = document.querySelector(`option[value="${productId}"]`);
    if (option) {
        document.getElementById('detail-name').textContent = option.textContent;
        document.getElementById('detail-unit').textContent = option.dataset.unit;
        document.getElementById('detail-category').textContent = option.dataset.category;
        document.getElementById('product-details').style.display = 'block';
        document.getElementById('add-new-product').style.display = 'none';
    }
}

function addProduct() {
    const productSelect = document.getElementById('product-select');
    const quantity = parseFloat(document.getElementById('product-quantity').value);
    const price = parseFloat(document.getElementById('product-price').value);
    
    if (!productSelect.value || !quantity || !price) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const option = productSelect.options[productSelect.selectedIndex];
    const productId = parseInt(productSelect.value);
    
    // Check if product already exists
    const existingIndex = invoiceItems.findIndex(item => item.product_id === productId);
    if (existingIndex !== -1) {
        invoiceItems[existingIndex].quantity += quantity;
    } else {
        invoiceItems.push({
            product_id: productId,
            name: option.textContent,
            barcode: option.dataset.barcode,
            unit: option.dataset.unit,
            quantity: quantity,
            price: price
        });
    }
    
    updateItemsTable();
    clearProductForm();
}

function updateItemsTable() {
    const tbody = document.getElementById('invoice-items-body');
    const noItems = document.getElementById('no-items');
    
    tbody.innerHTML = '';
    
    if (invoiceItems.length === 0) {
        noItems.style.display = 'block';
        return;
    }
    
    noItems.style.display = 'none';
    
    invoiceItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.name}</td>
            <td>${item.barcode}</td>
            <td>${item.unit}</td>
            <td>
                <input type="number" class="form-control item-quantity" value="${item.quantity}" 
                       min="1" step="0.01" style="width: 100px;" data-index="${index}">
            </td>
            <td>
                <input type="number" class="form-control item-price" value="${item.price}" 
                       min="0" step="0.01" style="width: 120px;" data-index="${index}">
            </td>
            <td class="item-total">${formatCurrency(item.quantity * item.price)}</td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger remove-item" data-index="${index}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // Add event listeners
    tbody.querySelectorAll('.item-quantity, .item-price').forEach(input => {
        input.addEventListener('input', function() {
            const index = parseInt(this.dataset.index);
            const quantity = parseFloat(tbody.querySelector(`input[data-index="${index}"].item-quantity`).value);
            const price = parseFloat(tbody.querySelector(`input[data-index="${index}"].item-price`).value);
            
            invoiceItems[index].quantity = quantity;
            invoiceItems[index].price = price;
            
            const totalCell = this.closest('tr').querySelector('.item-total');
            totalCell.textContent = formatCurrency(quantity * price);
            
            updateTotal();
        });
    });
    
    tbody.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            invoiceItems.splice(index, 1);
            updateItemsTable();
        });
    });
    
    updateTotal();
}

function updateTotal() {
    const total = invoiceItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    document.getElementById('total-amount').textContent = formatCurrency(total);
}

function clearProductForm() {
    document.getElementById('product-barcode').value = '';
    document.getElementById('product-select').selectedIndex = 0;
    document.getElementById('product-quantity').value = '1';
    document.getElementById('product-price').value = '';
    document.getElementById('product-details').style.display = 'none';
    document.getElementById('add-new-product').style.display = 'none';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}
</script>
{% endblock %}
