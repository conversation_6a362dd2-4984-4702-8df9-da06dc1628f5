from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, current_user, login_required
from datetime import datetime
from app import db
from app.auth import bp
from app.auth.forms import <PERSON>ginForm, UserForm, EditUserForm, ChangePasswordForm
from app.models import User
from app.utils import admin_required, can_edit_required

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            user.last_login = datetime.utcnow()
            db.session.commit()
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.index')
            flash(f'مرحباً {user.full_name}!', 'success')
            return redirect(next_page)
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    
    return render_template('auth/login.html', title='تسجيل الدخول', form=form)

@bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/users')
@login_required
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = User.query
    if search:
        query = query.filter(User.full_name.contains(search) | 
                           User.username.contains(search) | 
                           User.email.contains(search))
    
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False)
    
    return render_template('auth/users.html', title='إدارة المستخدمين', 
                         users=users, search=search)

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    form = UserForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            role=form.role.data,
            created_at=datetime.utcnow()
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash(f'تم إضافة المستخدم {user.full_name} بنجاح', 'success')
        return redirect(url_for('auth.users'))
    
    return render_template('auth/user_form.html', title='إضافة مستخدم جديد', 
                         form=form, action='add')

@bp.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    user = User.query.get_or_404(id)
    form = EditUserForm(user)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.role = form.role.data
        db.session.commit()
        flash(f'تم تحديث بيانات المستخدم {user.full_name} بنجاح', 'success')
        return redirect(url_for('auth.users'))
    
    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.full_name.data = user.full_name
        form.role.data = user.role
    
    return render_template('auth/user_form.html', title='تعديل المستخدم', 
                         form=form, action='edit', user=user)

@bp.route('/users/<int:id>/toggle')
@login_required
@admin_required
def toggle_user(id):
    user = User.query.get_or_404(id)
    if user.id == current_user.id:
        flash('لا يمكنك تعطيل حسابك الخاص', 'warning')
        return redirect(url_for('auth.users'))
    
    user.is_active = not user.is_active
    db.session.commit()
    status = 'تفعيل' if user.is_active else 'تعطيل'
    flash(f'تم {status} المستخدم {user.full_name} بنجاح', 'success')
    return redirect(url_for('auth.users'))

@bp.route('/users/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(id):
    user = User.query.get_or_404(id)
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'warning')
        return redirect(url_for('auth.users'))
    
    db.session.delete(user)
    db.session.commit()
    flash(f'تم حذف المستخدم {user.full_name} بنجاح', 'success')
    return redirect(url_for('auth.users'))

@bp.route('/profile')
@login_required
def profile():
    return render_template('auth/profile.html', title='الملف الشخصي')

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('تم تغيير كلمة المرور بنجاح', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('كلمة المرور الحالية غير صحيحة', 'danger')
    
    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)
